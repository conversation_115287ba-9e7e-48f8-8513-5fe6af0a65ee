import { createApp } from 'vue'
import { createPinia } from 'pinia' // Add Pinia import
import './style.css'
import App from './App.vue'
import router from './router' // Import the router instance
import 'vue-toastification/dist/index.css'; // Import the CSS FIRST
import Toast from 'vue-toastification'; // Import vue-toastification

// Filter out browser extension errors from console
const originalError = console.error;
console.error = function(...args) {
  const message = args.join(' ');

  // Filter out browser extension related errors
  if (message.includes('web_accessible_resources') ||
      message.includes('chrome-extension://invalid') ||
      message.includes('Denying load of') ||
      (message.includes('Failed to load resource: net::ERR_FAILED') && message.includes('chrome-extension'))) {
    // Silently ignore browser extension errors
    return;
  }

  // Log all other errors normally
  originalError.apply(console, args);
};

console.log('🚀 Surgery Scheduler Frontend Starting...');
console.log('🔧 Browser extension error filtering enabled');

const app = createApp(App);
const pinia = createPinia(); // Create Pinia instance

app.use(pinia); // Install Pinia BEFORE mounting
app.use(router); // Use the router
app.use(Toast); // Use vue-toastification

app.mount('#app');