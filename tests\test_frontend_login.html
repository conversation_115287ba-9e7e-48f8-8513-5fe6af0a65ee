<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surgery Scheduler - Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background-color: #f9f9f9;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .credentials {
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .credentials h2 {
            margin-top: 0;
            color: #2e7d32;
        }
        .credentials ul {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Surgery Scheduler - Login Test</h1>
        
        <div class="credentials">
            <h2>Test Credentials</h2>
            <ul>
                <li><strong>Admin:</strong> username=admin, password=admin123</li>
                <li><strong>User:</strong> username=user, password=user123</li>
                <li><strong>Surgeon:</strong> username=surgeon, password=surgeon123</li>
            </ul>
        </div>
        
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" placeholder="Enter username">
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter password">
        </div>
        
        <button id="loginBtn">Login</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('loginBtn').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            if (!username || !password) {
                resultDiv.innerHTML = '<strong>Error:</strong> Please enter both username and password.';
                resultDiv.classList.add('error');
                resultDiv.style.display = 'block';
                return;
            }
            
            resultDiv.innerHTML = 'Logging in...';
            resultDiv.classList.remove('error');
            resultDiv.style.display = 'block';
            
            try {
                // Create form data
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                // Make the login request
                const response = await fetch('http://127.0.0.1:8000/api/auth/token', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Login successful
                    resultDiv.innerHTML = `
                        <strong>Login successful!</strong>
                        <p>Access token received. You can now use this token to access protected endpoints.</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    // Get user info
                    const userResponse = await fetch('http://127.0.0.1:8000/api/users/me', {
                        headers: {
                            'Authorization': `Bearer ${data.access_token}`
                        }
                    });
                    
                    if (userResponse.ok) {
                        const userData = await userResponse.json();
                        resultDiv.innerHTML += `
                            <p><strong>User information:</strong></p>
                            <pre>${JSON.stringify(userData, null, 2)}</pre>
                        `;
                    }
                } else {
                    // Login failed
                    resultDiv.innerHTML = `
                        <strong>Login failed!</strong>
                        <p>Error: ${data.detail || 'Unknown error'}</p>
                    `;
                    resultDiv.classList.add('error');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <strong>Error:</strong>
                    <p>${error.message}</p>
                `;
                resultDiv.classList.add('error');
            }
        });
    </script>
</body>
</html>
