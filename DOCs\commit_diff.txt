commit 971ab075998ba6d7441cf6d589f2c4cb68745399
Author: <PERSON><PERSON> <<EMAIL>>
Date:   Sun May 25 20:30:06 2025 +0200

    started mobile optimization

diff --git a/MOBILE_RESPONSIVENESS_IMPLEMENTATION.md b/MO<PERSON>LE_RESPONSIVENESS_IMPLEMENTATION.md
new file mode 100644
index 0000000..f5607cb
--- /dev/null
+++ b/MO<PERSON>LE_RESPONSIVENESS_IMPLEMENTATION.md
@@ -0,0 +1,188 @@
+# Mobile Responsiveness Implementation
+
+## Overview
+
+This document outlines the comprehensive mobile responsiveness implementation for the Surgery Scheduling System. The implementation follows a mobile-first approach with progressive enhancement for larger screens.
+
+## Implementation Summary
+
+### 1. Global CSS Enhancements (`src/style.css`)
+
+#### Enhanced CSS Variables
+- Added mobile-specific breakpoints (`--breakpoint-xs`, `--breakpoint-sm`, etc.)
+- Added touch target sizes (`--touch-target-min`, `--touch-target-comfortable`)
+- Added mobile-specific spacing variables
+- Enhanced typography scale with additional font sizes
+
+#### Responsive Utility Classes
+- `.mobile-only`, `.tablet-only`, `.desktop-only` - Display utilities
+- `.btn-touch` - Touch-friendly button sizing
+- `.form-control-mobile` - Mobile-optimized form controls
+- `.mobile-p-*`, `.mobile-m-*` - Mobile-specific spacing utilities
+- `.responsive-grid`, `.responsive-flex` - Responsive layout utilities
+
+#### Media Query Structure
+- Mobile-first approach with progressive enhancement
+- Breakpoints: 480px (small mobile), 768px (tablet), 1024px (desktop), 1200px+ (large desktop)
+
+### 2. AppLayout Component (`src/components/AppLayout.vue`)
+
+#### Mobile Navigation Features
+- **Hamburger Menu**: Animated hamburger icon for mobile navigation
+- **Mobile Sidebar**: Slide-out navigation with overlay
+- **Mobile Search**: Dedicated mobile search overlay
+- **Touch-Friendly**: All interactive elements meet touch target requirements
+
+#### Responsive Behavior
+- **Auto-responsive**: Automatically detects screen size and adjusts layout
+- **Orientation Support**: Handles both portrait and landscape orientations
+- **Touch Detection**: Optimizes interactions for touch devices
+
+#### Key Features
+- Mobile navigation overlay with backdrop
+- Collapsible search functionality
+- Notification badges
+- User profile dropdown optimization
+- Safe area support for modern mobile devices
+
+### 3. SchedulingScreen Component (`src/components/SchedulingScreen.vue`)
+
+#### Layout Adaptations
+- **Tablet (768px-1200px)**: Stacked panels with reordered priority
+- **Mobile (Γëñ768px)**: Single-column layout with optimized heights
+- **Small Mobile (Γëñ480px)**: Compact spacing and simplified interface
+
+#### Mobile-Specific Features
+- Touch-friendly form controls
+- Full-width action buttons
+- Optimized panel heights for mobile viewing
+- Landscape orientation support
+
+### 4. GanttChart Component (`src/components/GanttChart.vue`)
+
+#### Mobile Optimizations
+- **Responsive Timeline**: Adjustable time markers for different screen sizes
+- **Touch Interactions**: Enhanced touch support for drag-and-drop
+- **Simplified Mobile View**: Optional simplified view for small screens
+- **Horizontal Scrolling**: Touch-friendly scrolling with momentum
+
+#### Adaptive Features
+- Smaller time intervals on mobile
+- Larger touch targets for surgery blocks
+- Responsive legend layout
+- Orientation-aware adjustments
+
+### 5. AnalyticsDashboard Component (`src/components/AnalyticsDashboard.vue`)
+
+#### Responsive Charts and Metrics
+- **Adaptive Grid**: Responsive grid layout for metrics cards
+- **Mobile Charts**: Optimized chart sizes for mobile viewing
+- **Touch Controls**: Touch-friendly date range selectors
+- **Stacked Layout**: Single-column layout on mobile
+
+#### Mobile Features
+- Compact metric cards
+- Full-width buttons
+- Responsive date picker
+- Optimized chart legends
+
+## Technical Implementation Details
+
+### Breakpoint Strategy
+```css
+/* Mobile-first approach */
+@media (max-width: 480px) { /* Small mobile */ }
+@media (max-width: 768px) { /* Mobile */ }
+@media (max-width: 1024px) { /* Tablet */ }
+@media (max-width: 1200px) { /* Small desktop */ }
+```
+
+### Touch Target Guidelines
+- Minimum touch target: 44px (iOS/Android standard)
+- Comfortable touch target: 48px
+- All interactive elements meet accessibility standards
+
+### Performance Considerations
+- CSS-only animations for smooth performance
+- Hardware acceleration for transforms
+- Optimized media queries to minimize reflows
+- Touch momentum scrolling enabled
+
+## Testing and Validation
+
+### Mobile Test Component
+Created `MobileTestComponent.vue` for comprehensive testing:
+- Screen size detection
+- Device type identification
+- Touch capability detection
+- Responsive grid testing
+- Touch target validation
+
+### Browser Testing
+- Chrome DevTools mobile simulation
+- Safari iOS simulator
+- Firefox responsive design mode
+- Real device testing recommended
+
+## Accessibility Features
+
+### Touch Accessibility
+- Minimum 44px touch targets
+- Clear visual feedback for interactions
+- Proper focus management
+- Screen reader compatibility
+
+### Visual Accessibility
+- High contrast ratios maintained
+- Scalable text and UI elements
+- Clear visual hierarchy
+- Reduced motion support
+
+## Future Enhancements
+
+### Potential Improvements
+1. **Progressive Web App (PWA)** features
+2. **Offline functionality** for critical features
+3. **Advanced touch gestures** (pinch-to-zoom, swipe navigation)
+4. **Device-specific optimizations** (iOS/Android)
+5. **Performance monitoring** for mobile devices
+
+### Recommended Testing
+1. Test on actual mobile devices
+2. Validate touch interactions
+3. Check performance on slower devices
+4. Verify accessibility compliance
+5. Test in various orientations
+
+## Usage Guidelines
+
+### For Developers
+1. Always test responsive changes on multiple screen sizes
+2. Use the mobile test component for validation
+3. Follow the established breakpoint strategy
+4. Maintain touch target requirements
+5. Test with both mouse and touch interactions
+
+### For Users
+- The interface automatically adapts to your device
+- Use the hamburger menu (Γÿ░) for navigation on mobile
+- Tap the search icon (≡ƒöì) for mobile search
+- All features are accessible on mobile devices
+- Rotate your device for optimal viewing in some screens
+
+## Browser Support
+
+### Supported Browsers
+- **iOS Safari**: 12+
+- **Chrome Mobile**: 70+
+- **Firefox Mobile**: 68+
+- **Samsung Internet**: 10+
+- **Edge Mobile**: 79+
+
+### CSS Features Used
+- CSS Grid with fallbacks
+- Flexbox
+- CSS Custom Properties (CSS Variables)
+- Media Queries Level 4
+- Touch-action property
+- Safe area insets (for modern devices)
diff --git a/implimentation.txt b/implementation.txt
similarity index 100%
rename from implimentation.txt
rename to implementation.txt
diff --git a/package-lock.json b/package-lock.json
index 5740407..1ad02fc 100644
--- a/package-lock.json
+++ b/package-lock.json
@@ -8,6 +8,7 @@
       "name": "myapp",
       "version": "0.0.0",
       "dependencies": {
+        "@infectoone/vue-ganttastic": "^2.3.2",
         "pinia": "^3.0.2",
         "vue": "^3.5.13",
         "vue-router": "^4.5.1",
@@ -833,6 +834,19 @@
         "url": "https://github.com/sponsors/nzakas"
       }
     },
+    "node_modules/@infectoone/vue-ganttastic": {
+      "version": "2.3.2",
+      "resolved": "https://registry.npmjs.org/@infectoone/vue-ganttastic/-/vue-ganttastic-2.3.2.tgz",
+      "integrity": "sha512-krxHdlZvo4cdS4axQ99qb756RzwieI7LcyY2vAIehJ5Sxd/jz5Pu/vTplTC0Rxqj8T4v1knYPK9uvTMkQYWYng==",
+      "license": "MIT",
+      "dependencies": {
+        "@vueuse/core": "^9.1.1"
+      },
+      "peerDependencies": {
+        "dayjs": "^1.11.5",
+        "vue": "^3.2.40"
+      }
+    },
     "node_modules/@isaacs/cliui": {
       "version": "8.0.2",
       "resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz",
@@ -1189,6 +1203,12 @@
       "dev": true,
       "license": "MIT"
     },
+    "node_modules/@types/web-bluetooth": {
+      "version": "0.0.16",
+      "resolved": "https://registry.npmjs.org/@types/web-bluetooth/-/web-bluetooth-0.0.16.tgz",
+      "integrity": "sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==",
+      "license": "MIT"
+    },
     "node_modules/@vitejs/plugin-vue": {
       "version": "5.2.4",
       "resolved": "https://registry.npmjs.org/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz",
@@ -1503,6 +1523,94 @@
         "vue-component-type-helpers": "^2.0.0"
       }
     },
+    "node_modules/@vueuse/core": {
+      "version": "9.13.0",
+      "resolved": "https://registry.npmjs.org/@vueuse/core/-/core-9.13.0.tgz",
+      "integrity": "sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==",
+      "license": "MIT",
+      "dependencies": {
+        "@types/web-bluetooth": "^0.0.16",
+        "@vueuse/metadata": "9.13.0",
+        "@vueuse/shared": "9.13.0",
+        "vue-demi": "*"
+      },
+      "funding": {
+        "url": "https://github.com/sponsors/antfu"
+      }
+    },
+    "node_modules/@vueuse/core/node_modules/vue-demi": {
+      "version": "0.14.10",
+      "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz",
+      "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==",
+      "hasInstallScript": true,
+      "license": "MIT",
+      "bin": {
+        "vue-demi-fix": "bin/vue-demi-fix.js",
+        "vue-demi-switch": "bin/vue-demi-switch.js"
+      },
+      "engines": {
+        "node": ">=12"
+      },
+      "funding": {
+        "url": "https://github.com/sponsors/antfu"
+      },
+      "peerDependencies": {
+        "@vue/composition-api": "^1.0.0-rc.1",
+        "vue": "^3.0.0-0 || ^2.6.0"
+      },
+      "peerDependenciesMeta": {
+        "@vue/composition-api": {
+          "optional": true
+        }
+      }
+    },
+    "node_modules/@vueuse/metadata": {
+      "version": "9.13.0",
+      "resolved": "https://registry.npmjs.org/@vueuse/metadata/-/metadata-9.13.0.tgz",
+      "integrity": "sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==",
+      "license": "MIT",
+      "funding": {
+        "url": "https://github.com/sponsors/antfu"
+      }
+    },
+    "node_modules/@vueuse/shared": {
+      "version": "9.13.0",
+      "resolved": "https://registry.npmjs.org/@vueuse/shared/-/shared-9.13.0.tgz",
+      "integrity": "sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==",
+      "license": "MIT",
+      "dependencies": {
+        "vue-demi": "*"
+      },
+      "funding": {
+        "url": "https://github.com/sponsors/antfu"
+      }
+    },
+    "node_modules/@vueuse/shared/node_modules/vue-demi": {
+      "version": "0.14.10",
+      "resolved": "https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.10.tgz",
+      "integrity": "sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==",
+      "hasInstallScript": true,
+      "license": "MIT",
+      "bin": {
+        "vue-demi-fix": "bin/vue-demi-fix.js",
+        "vue-demi-switch": "bin/vue-demi-switch.js"
+      },
+      "engines": {
+        "node": ">=12"
+      },
+      "funding": {
+        "url": "https://github.com/sponsors/antfu"
+      },
+      "peerDependencies": {
+        "@vue/composition-api": "^1.0.0-rc.1",
+        "vue": "^3.0.0-0 || ^2.6.0"
+      },
+      "peerDependenciesMeta": {
+        "@vue/composition-api": {
+          "optional": true
+        }
+      }
+    },
     "node_modules/abbrev": {
       "version": "2.0.0",
       "resolved": "https://registry.npmjs.org/abbrev/-/abbrev-2.0.0.tgz",
@@ -1832,6 +1940,13 @@
         "node": ">=18"
       }
     },
+    "node_modules/dayjs": {
+      "version": "1.11.13",
+      "resolved": "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz",
+      "integrity": "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==",
+      "license": "MIT",
+      "peer": true
+    },
     "node_modules/debug": {
       "version": "4.4.1",
       "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz",
diff --git a/package.json b/package.json
index 4d68f8e..45d5a11 100644
--- a/package.json
+++ b/package.json
@@ -10,6 +10,7 @@
     "test": "vitest"
   },
   "dependencies": {
+    "@infectoone/vue-ganttastic": "^2.3.2",
     "pinia": "^3.0.2",
     "vue": "^3.5.13",
     "vue-router": "^4.5.1",
diff --git a/src/components/AdministrationScreen.vue b/src/components/AdministrationScreen.vue
index bc82551..f6f0abd 100644
--- a/src/components/AdministrationScreen.vue
+++ b/src/components/AdministrationScreen.vue
@@ -1,22 +1,104 @@
 <template>
-  <div class="section-container">
+  <div class="administration-screen">
     <h1>Administration</h1>
-    <div class="admin-sections">
-      <!-- Placeholder navigation for different admin sections -->
-      <p>Admin sections placeholder:</p>
-      <button>User Management</button>
-      <button>Role Management</button>
-      <button>System Settings</button>
+    <div class="admin-navigation">
+      <button
+        v-for="tab in tabs"
+        :key="tab.name"
+        @click="activeTab = tab.name"
+        :class="['nav-button', { active: activeTab === tab.name }]"
+      >
+        {{ tab.label }}
+      </button>
+    </div>
+    <div class="admin-content">
+      <!-- Content for each tab will be rendered here -->
+      <div v-if="activeTab === 'userManagement'">
+        <p>User Management Content (Placeholder)</p>
+      </div>
+      <div v-if="activeTab === 'roleManagement'">
+        <p>Role Management Content (Placeholder)</p>
+      </div>
+      <div v-if="activeTab === 'systemSettings'">
+        <p>System Settings Content (Placeholder)</p>
+      </div>
+      <div v-if="activeTab === 'auditLogs'">
+        <p>Audit Logs Content (Placeholder)</p>
+      </div>
     </div>
   </div>
 </template>
 
 <script setup>
-// Component logic will go here later
+import { ref } from 'vue';
+
+const activeTab = ref('userManagement'); // Default active tab
+
+const tabs = [
+  { name: 'userManagement', label: 'User Management' },
+  { name: 'roleManagement', label: 'Role Management' },
+  { name: 'systemSettings', label: 'System Settings' },
+  { name: 'auditLogs', label: 'Audit Logs' },
+];
+
+// Later, we will import and use actual components for each tab:
+// import UserManagement from './UserManagement.vue';
+// import RoleManagement from './RoleManagement.vue';
+// import SystemConfiguration from './SystemConfiguration.vue';
+// import AuditLogViewer from './AuditLogViewer.vue';
 </script>
 
 <style scoped>
-.section-container {
+.administration-screen {
   padding: 20px;
+  font-family: Arial, sans-serif;
+}
+
+.administration-screen h1 {
+  color: #333;
+  margin-bottom: 20px;
+  border-bottom: 2px solid #eee;
+  padding-bottom: 10px;
+}
+
+.admin-navigation {
+  display: flex;
+  margin-bottom: 20px;
+  border-bottom: 1px solid #ccc;
+}
+
+.nav-button {
+  padding: 10px 20px;
+  cursor: pointer;
+  border: none;
+  background-color: transparent;
+  font-size: 16px;
+  color: #555;
+  margin-right: 5px; /* Spacing between buttons */
+  border-bottom: 3px solid transparent; /* For active state indicator */
+  transition: color 0.3s ease, border-bottom-color 0.3s ease;
+}
+
+.nav-button:hover {
+  color: #007bff;
+}
+
+.nav-button.active {
+  color: #007bff;
+  border-bottom-color: #007bff;
+  font-weight: bold;
+}
+
+.admin-content {
+  padding: 20px;
+  border: 1px solid #ddd;
+  border-radius: 4px;
+  background-color: #f9f9f9;
+  min-height: 300px; /* Give some space for content */
+}
+
+.admin-content p {
+  font-size: 1.1em;
+  color: #666;
 }
 </style>
\ No newline at end of file
diff --git a/src/components/AnalyticsDashboard.vue b/src/components/AnalyticsDashboard.vue
index a07d72a..658f097 100644
--- a/src/components/AnalyticsDashboard.vue
+++ b/src/components/AnalyticsDashboard.vue
@@ -1,25 +1,25 @@
 <template>
   <div class="analytics-dashboard">
     <h1>Analytics Dashboard</h1>
-    
+
     <!-- Date Range Selector -->
     <div class="date-range-selector">
       <h3>Date Range</h3>
       <div class="date-inputs">
         <div class="date-input">
           <label for="start-date">Start Date</label>
-          <input 
-            type="date" 
-            id="start-date" 
+          <input
+            type="date"
+            id="start-date"
             :value="formatDateForInput(dateRange.start)"
             @change="updateStartDate"
           >
         </div>
         <div class="date-input">
           <label for="end-date">End Date</label>
-          <input 
-            type="date" 
-            id="end-date" 
+          <input
+            type="date"
+            id="end-date"
             :value="formatDateForInput(dateRange.end)"
             @change="updateEndDate"
           >
@@ -33,19 +33,19 @@
         <button @click="setQuickRange('lastMonth')">Last Month</button>
       </div>
     </div>
-    
+
     <!-- Loading Indicator -->
     <div v-if="isLoading" class="loading-overlay">
       <div class="spinner"></div>
       <p>Loading analytics data...</p>
     </div>
-    
+
     <!-- Error Message -->
     <div v-else-if="error" class="error-message">
       <p>{{ error }}</p>
       <button @click="loadAnalyticsData">Retry</button>
     </div>
-    
+
     <!-- Dashboard Content -->
     <div v-else class="dashboard-content">
       <!-- Summary Metrics -->
@@ -57,7 +57,7 @@
             {{ surgeryTrend.value }}% {{ surgeryTrend.direction === 'up' ? 'Γåæ' : 'Γåô' }}
           </div>
         </div>
-        
+
         <div class="metric-card">
           <h3>Average OR Utilization</h3>
           <div class="metric-value">{{ formatPercentage(averageORUtilization) }}</div>
@@ -65,7 +65,7 @@
             {{ utilizationTrend.value }}% {{ utilizationTrend.direction === 'up' ? 'Γåæ' : 'Γåô' }}
           </div>
         </div>
-        
+
         <div class="metric-card">
           <h3>On-Time Start Rate</h3>
           <div class="metric-value">{{ formatPercentage(onTimeStartRate) }}</div>
@@ -73,16 +73,75 @@
             {{ onTimeTrend.value }}% {{ onTimeTrend.direction === 'up' ? 'Γåæ' : 'Γåô' }}
           </div>
         </div>
-        
+
         <div class="metric-card">
-          <h3>Avg. Turnaround Time</h3>
-          <div class="metric-value">{{ averageTurnaround }} min</div>
+          <h3>Average SDST</h3>
+          <div class="metric-value">{{ Math.round(kpiData?.averageSDST || 0) }} min</div>
           <div class="metric-trend" :class="turnaroundTrend.direction === 'up' ? 'down' : 'up'">
             {{ turnaroundTrend.value }}% {{ turnaroundTrend.direction === 'up' ? 'Γåæ' : 'Γåô' }}
           </div>
         </div>
+
+        <div class="metric-card">
+          <h3>Daily Conflicts</h3>
+          <div class="metric-value">{{ Math.round(kpiData?.conflictRate || 0) }}</div>
+          <div class="metric-trend" :class="conflictTrend.direction">
+            {{ conflictTrend.value }}% {{ conflictTrend.direction === 'up' ? 'Γåæ' : 'Γåô' }}
+          </div>
+        </div>
       </div>
-      
+
+      <!-- SDST Insights Section -->
+      <div v-if="sdstEfficiency" class="sdst-insights">
+        <h3>SDST Optimization Insights</h3>
+        <div class="insights-grid">
+          <div class="insight-card">
+            <h4>Most Efficient Transition</h4>
+            <div class="transition-info">
+              <span class="transition">{{ sdstEfficiency?.mostEfficientTransition?.from }} ΓåÆ {{ sdstEfficiency?.mostEfficientTransition?.to }}</span>
+              <span class="time">{{ sdstEfficiency?.mostEfficientTransition?.averageTime }} min</span>
+            </div>
+          </div>
+
+          <div class="insight-card">
+            <h4>Least Efficient Transition</h4>
+            <div class="transition-info">
+              <span class="transition">{{ sdstEfficiency?.leastEfficientTransition?.from }} ΓåÆ {{ sdstEfficiency?.leastEfficientTransition?.to }}</span>
+              <span class="time">{{ sdstEfficiency?.leastEfficientTransition?.averageTime }} min</span>
+            </div>
+          </div>
+
+          <div class="insight-card">
+            <h4>Potential Daily Savings</h4>
+            <div class="savings-info">
+              <span class="savings">{{ sdstEfficiency?.potentialSavings }} min</span>
+              <span class="description">Through optimization</span>
+            </div>
+          </div>
+        </div>
+      </div>
+
+      <!-- Optimization Suggestions Component -->
+      <OptimizationSuggestions />
+
+      <!-- Optimization Opportunities -->
+      <div v-if="optimizationOpportunities && optimizationOpportunities.length > 0" class="optimization-opportunities">
+        <h3>Optimization Opportunities</h3>
+        <div class="opportunities-list">
+          <div v-for="opportunity in optimizationOpportunities.slice(0, 3)" :key="opportunity.type"
+               class="opportunity-card" :class="`priority-${opportunity.priority.toLowerCase()}`">
+            <div class="opportunity-header">
+              <h4>{{ opportunity.type }}</h4>
+              <span class="priority-badge">{{ opportunity.priority }}</span>
+            </div>
+            <p class="opportunity-description">{{ opportunity.description }}</p>
+            <div class="opportunity-savings">
+              <strong>Potential Savings: {{ opportunity.potentialSavings }}</strong>
+            </div>
+          </div>
+        </div>
+      </div>
+
       <!-- Main Charts -->
       <div class="chart-row">
         <div class="chart-container">
@@ -90,14 +149,14 @@
           <div class="chart-placeholder">
             <!-- Chart would be rendered here using a charting library -->
             <div class="chart-mock">
-              <div v-for="(value, index) in dailySurgeryData" :key="index" 
-                   class="chart-bar" 
+              <div v-for="(value, index) in dailySurgeryData" :key="index"
+                   class="chart-bar"
                    :style="{ height: `${value * 100}%` }">
               </div>
             </div>
           </div>
         </div>
-        
+
         <div class="chart-container">
           <h3>OR Utilization by Room</h3>
           <div class="chart-placeholder">
@@ -114,16 +173,16 @@
           </div>
         </div>
       </div>
-      
+
       <div class="chart-row">
         <div class="chart-container">
           <h3>Surgery Type Distribution</h3>
           <div class="chart-placeholder">
             <!-- Chart would be rendered here using a charting library -->
             <div class="chart-mock pie">
-              <div v-for="(segment, index) in surgeryTypeData" :key="index" 
+              <div v-for="(segment, index) in surgeryTypeData" :key="index"
                    class="pie-segment"
-                   :style="{ 
+                   :style="{
                      backgroundColor: segment.color,
                      transform: `rotate(${segment.startAngle}deg)`,
                      clipPath: `polygon(50% 50%, 100% 0, 100% 100%, 0 100%, 0 0)`
@@ -138,7 +197,7 @@
             </div>
           </div>
         </div>
-        
+
         <div class="chart-container">
           <h3>Surgeon Performance</h3>
           <div class="chart-placeholder">
@@ -155,7 +214,7 @@
           </div>
         </div>
       </div>
-      
+
       <!-- Report Links -->
       <div class="report-links">
         <h3>Detailed Reports</h3>
@@ -174,10 +233,23 @@ import { ref, computed, onMounted } from 'vue';
 import { useRouter } from 'vue-router';
 import { useAnalyticsStore } from '@/stores/analyticsStore';
 import { storeToRefs } from 'pinia';
+import OptimizationSuggestions from './OptimizationSuggestions.vue';
 
 const router = useRouter();
 const analyticsStore = useAnalyticsStore();
-const { isLoading, error, dateRange, cachedData } = storeToRefs(analyticsStore);
+const {
+  isLoading,
+  error,
+  dateRange,
+  cachedData,
+  keyPerformanceIndicators,
+  sdstPatterns,
+  sdstEfficiency,
+  resourceOptimization,
+  schedulingEfficiency,
+  conflictAnalysis,
+  optimizationOpportunities
+} = storeToRefs(analyticsStore);
 
 // Mock data for charts (in a real app, this would come from the store)
 const dailySurgeryData = ref([0.5, 0.7, 0.6, 0.8, 0.9, 0.4, 0.6]);
@@ -202,10 +274,20 @@ const surgeonPerformanceData = ref([
   { name: 'Dr. Wong', value: 0.4, surgeries: 20 },
 ]);
 
+// Computed properties for enhanced analytics
+const kpiData = computed(() => {
+  try {
+    return keyPerformanceIndicators?.value || null;
+  } catch (error) {
+    console.warn('Failed to access keyPerformanceIndicators:', error);
+    return null;
+  }
+});
+
 // Summary metrics
 const totalSurgeries = ref(133);
-const averageORUtilization = ref(0.78);
-const onTimeStartRate = ref(0.82);
+const averageORUtilization = computed(() => kpiData.value?.averageORUtilization || 0.78);
+const onTimeStartRate = computed(() => kpiData.value?.onTimeStartRate || 0.82);
 const averageTurnaround = ref(24);
 
 // Trend data (would be calculated from historical data)
@@ -213,6 +295,7 @@ const surgeryTrend = ref({ value: 12, direction: 'up' });
 const utilizationTrend = ref({ value: 5, direction: 'up' });
 const onTimeTrend = ref({ value: 3, direction: 'up' });
 const turnaroundTrend = ref({ value: 8, direction: 'down' });
+const conflictTrend = ref({ value: 15, direction: 'down' });
 
 // Load analytics data on component mount
 onMounted(async () => {
@@ -222,7 +305,7 @@ onMounted(async () => {
 // Load analytics data
 const loadAnalyticsData = async () => {
   await analyticsStore.loadAnalyticsData();
-  
+
   // In a real app, we would update the chart data from the store
   // For now, we'll use the mock data
 };
@@ -265,7 +348,7 @@ const applyDateRange = async () => {
 const setQuickRange = async (range) => {
   const today = new Date();
   let start, end;
-  
+
   switch (range) {
     case 'last7':
       start = new Date(today);
@@ -288,7 +371,7 @@ const setQuickRange = async (range) => {
     default:
       return;
   }
-  
+
   dateRange.value.start = start;
   dateRange.value.end = end;
   await applyDateRange();
@@ -423,7 +506,7 @@ h3 {
 /* Summary Metrics */
 .summary-metrics {
   display: grid;
-  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
+  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
   gap: var(--spacing-md);
   margin-bottom: var(--spacing-lg);
 }
@@ -454,6 +537,150 @@ h3 {
   color: var(--color-error);
 }
 
+/* SDST Insights */
+.sdst-insights {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.insights-grid {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
+  gap: var(--spacing-md);
+  margin-top: var(--spacing-md);
+}
+
+.insight-card {
+  background-color: var(--color-background);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  border-left: 4px solid var(--color-primary);
+}
+
+.insight-card h4 {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-text);
+  font-size: var(--font-size-sm);
+  font-weight: var(--font-weight-medium);
+}
+
+.transition-info {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-xs);
+}
+
+.transition {
+  font-weight: var(--font-weight-medium);
+  color: var(--color-primary);
+}
+
+.time {
+  font-size: var(--font-size-lg);
+  font-weight: var(--font-weight-bold);
+  color: var(--color-text);
+}
+
+.savings-info {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-xs);
+}
+
+.savings {
+  font-size: var(--font-size-lg);
+  font-weight: var(--font-weight-bold);
+  color: var(--color-success);
+}
+
+.description {
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+}
+
+/* Optimization Opportunities */
+.optimization-opportunities {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.opportunities-list {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
+  gap: var(--spacing-md);
+  margin-top: var(--spacing-md);
+}
+
+.opportunity-card {
+  background-color: var(--color-background);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  border-left: 4px solid var(--color-border);
+}
+
+.opportunity-card.priority-high {
+  border-left-color: var(--color-error);
+}
+
+.opportunity-card.priority-medium {
+  border-left-color: var(--color-warning);
+}
+
+.opportunity-card.priority-low {
+  border-left-color: var(--color-success);
+}
+
+.opportunity-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  margin-bottom: var(--spacing-sm);
+}
+
+.opportunity-header h4 {
+  margin: 0;
+  color: var(--color-text);
+  font-size: var(--font-size-md);
+}
+
+.priority-badge {
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  font-size: var(--font-size-xs);
+  font-weight: var(--font-weight-medium);
+  text-transform: uppercase;
+}
+
+.priority-high .priority-badge {
+  background-color: rgba(var(--color-error-rgb), 0.1);
+  color: var(--color-error);
+}
+
+.priority-medium .priority-badge {
+  background-color: rgba(var(--color-warning-rgb), 0.1);
+  color: var(--color-warning);
+}
+
+.priority-low .priority-badge {
+  background-color: rgba(var(--color-success-rgb), 0.1);
+  color: var(--color-success);
+}
+
+.opportunity-description {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-text-secondary);
+  line-height: 1.5;
+}
+
+.opportunity-savings {
+  color: var(--color-success);
+  font-size: var(--font-size-sm);
+}
+
 /* Charts */
 .chart-row {
   display: grid;
@@ -598,29 +825,328 @@ h3 {
   background-color: var(--color-primary-dark);
 }
 
-/* Responsive adjustments */
+/* Enhanced Mobile Responsive Design */
+
+/* Tablet adjustments (768px - 1024px) */
+@media (max-width: 1024px) {
+  .analytics-dashboard {
+    padding: var(--spacing-sm);
+  }
+
+  .chart-row {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-md);
+  }
+
+  .summary-metrics {
+    grid-template-columns: repeat(3, 1fr);
+    gap: var(--spacing-sm);
+  }
+
+  .insights-grid {
+    grid-template-columns: 1fr;
+  }
+
+  .opportunities-list {
+    grid-template-columns: 1fr;
+  }
+}
+
+/* Mobile adjustments (up to 768px) */
 @media (max-width: 768px) {
+  .analytics-dashboard {
+    padding: var(--spacing-sm);
+  }
+
+  h1 {
+    font-size: var(--font-size-xl);
+    margin-bottom: var(--spacing-md);
+  }
+
+  /* Date range selector mobile optimization */
+  .date-range-selector {
+    padding: var(--spacing-sm);
+  }
+
   .date-inputs {
     flex-direction: column;
     gap: var(--spacing-sm);
   }
-  
-  .chart-row {
-    grid-template-columns: 1fr;
+
+  .date-input input,
+  .apply-button {
+    min-height: var(--touch-target-comfortable);
+    font-size: var(--font-size-base);
+    padding: var(--spacing-sm) var(--spacing-md);
   }
-  
+
+  .quick-ranges {
+    flex-wrap: wrap;
+    gap: var(--spacing-xs);
+  }
+
+  .quick-ranges button {
+    min-height: var(--touch-target-min);
+    padding: var(--spacing-sm) var(--spacing-md);
+    font-size: var(--font-size-sm);
+    flex: 1;
+    min-width: 120px;
+  }
+
+  /* Summary metrics mobile layout */
   .summary-metrics {
     grid-template-columns: repeat(2, 1fr);
+    gap: var(--spacing-sm);
+  }
+
+  .metric-card {
+    padding: var(--spacing-sm);
+  }
+
+  .metric-card h3 {
+    font-size: var(--font-size-sm);
+    margin-bottom: var(--spacing-xs);
+  }
+
+  .metric-value {
+    font-size: 1.5rem;
+    margin: var(--spacing-xs) 0;
+  }
+
+  .metric-trend {
+    font-size: var(--font-size-xs);
+  }
+
+  /* SDST insights mobile layout */
+  .sdst-insights {
+    padding: var(--spacing-sm);
+  }
+
+  .insights-grid {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-sm);
+  }
+
+  .insight-card {
+    padding: var(--spacing-sm);
+  }
+
+  .insight-card h4 {
+    font-size: var(--font-size-xs);
+  }
+
+  .time,
+  .savings {
+    font-size: var(--font-size-base);
+  }
+
+  /* Optimization opportunities mobile layout */
+  .optimization-opportunities {
+    padding: var(--spacing-sm);
+  }
+
+  .opportunities-list {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-sm);
+  }
+
+  .opportunity-card {
+    padding: var(--spacing-sm);
+  }
+
+  .opportunity-header h4 {
+    font-size: var(--font-size-sm);
+  }
+
+  .priority-badge {
+    font-size: 10px;
+    padding: 2px var(--spacing-xs);
+  }
+
+  /* Charts mobile optimization */
+  .chart-row {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-sm);
+  }
+
+  .chart-container {
+    padding: var(--spacing-sm);
+  }
+
+  .chart-container h3 {
+    font-size: var(--font-size-base);
+    margin-bottom: var(--spacing-sm);
+  }
+
+  .chart-placeholder {
+    height: 250px;
+  }
+
+  .chart-mock {
+    padding: var(--spacing-sm);
+  }
+
+  .chart-mock.horizontal .chart-label {
+    width: 60px;
+    font-size: var(--font-size-xs);
+  }
+
+  .chart-mock.horizontal .chart-value {
+    width: 60px;
+    font-size: var(--font-size-xs);
+  }
+
+  .chart-mock.pie {
+    width: 150px;
+    height: 150px;
+  }
+
+  .pie-legend {
+    top: 170px;
+    gap: var(--spacing-xs);
+  }
+
+  .legend-item {
+    font-size: var(--font-size-xs);
+  }
+
+  /* Report links mobile layout */
+  .report-links {
+    padding: var(--spacing-sm);
+  }
+
+  .report-buttons {
+    flex-direction: column;
+    gap: var(--spacing-sm);
+  }
+
+  .report-buttons button {
+    width: 100%;
+    min-height: var(--touch-target-comfortable);
+    padding: var(--spacing-sm) var(--spacing-md);
+    font-size: var(--font-size-base);
   }
 }
 
+/* Small mobile adjustments (up to 480px) */
 @media (max-width: 480px) {
+  .analytics-dashboard {
+    padding: var(--spacing-xs);
+  }
+
+  h1 {
+    font-size: var(--font-size-lg);
+    text-align: center;
+  }
+
+  /* Single column layout for metrics */
   .summary-metrics {
     grid-template-columns: 1fr;
+    gap: var(--spacing-xs);
   }
-  
-  .quick-ranges {
-    flex-wrap: wrap;
+
+  .metric-card {
+    padding: var(--spacing-xs);
+  }
+
+  .metric-value {
+    font-size: 1.25rem;
+  }
+
+  /* Compact date range selector */
+  .date-range-selector {
+    padding: var(--spacing-xs);
+  }
+
+  .quick-ranges button {
+    min-width: 100px;
+    font-size: var(--font-size-xs);
+    padding: var(--spacing-xs) var(--spacing-sm);
+  }
+
+  /* Compact charts */
+  .chart-placeholder {
+    height: 200px;
+  }
+
+  .chart-mock.pie {
+    width: 120px;
+    height: 120px;
+  }
+
+  .pie-legend {
+    top: 140px;
+  }
+
+  /* Compact insight cards */
+  .insight-card,
+  .opportunity-card {
+    padding: var(--spacing-xs);
+  }
+
+  .insight-card h4,
+  .opportunity-header h4 {
+    font-size: var(--font-size-xs);
+  }
+
+  .time,
+  .savings {
+    font-size: var(--font-size-sm);
+  }
+
+  .opportunity-description {
+    font-size: var(--font-size-xs);
+    line-height: 1.4;
+  }
+}
+
+/* Touch-specific enhancements */
+@media (hover: none) and (pointer: coarse) {
+  .quick-ranges button:active,
+  .apply-button:active,
+  .report-buttons button:active {
+    transform: scale(0.95);
+    transition: transform 0.1s ease;
+  }
+
+  .metric-card:active {
+    transform: scale(0.98);
+    transition: transform 0.1s ease;
+  }
+}
+
+/* Landscape orientation for mobile */
+@media (max-height: 500px) and (orientation: landscape) {
+  .analytics-dashboard {
+    padding: var(--spacing-xs);
+  }
+
+  .date-inputs {
+    flex-direction: row;
+    gap: var(--spacing-sm);
+  }
+
+  .summary-metrics {
+    grid-template-columns: repeat(5, 1fr);
+    gap: var(--spacing-xs);
+  }
+
+  .metric-card {
+    padding: var(--spacing-xs);
+  }
+
+  .metric-value {
+    font-size: 1rem;
+    margin: 2px 0;
+  }
+
+  .chart-placeholder {
+    height: 180px;
+  }
+
+  .insights-grid,
+  .opportunities-list {
+    grid-template-columns: repeat(2, 1fr);
   }
 }
 </style>
diff --git a/src/components/AppLayout.vue b/src/components/AppLayout.vue
index a54df7c..36ded38 100644
--- a/src/components/AppLayout.vue
+++ b/src/components/AppLayout.vue
@@ -1,47 +1,171 @@
 <template>
-  <div :class="['app-layout', { 'sidebar-collapsed': isSidebarCollapsed }]">
+  <div :class="['app-layout', {
+    'sidebar-collapsed': isSidebarCollapsed,
+    'mobile-nav-open': isMobileNavOpen,
+    'is-mobile': isMobile
+  }]">
     <header class="top-nav-bar">
       <div class="app-brand">
-         <button @click="toggleSidebar" class="icon-button toggle-sidebar-button" aria-label="Toggle Sidebar">
-             <!-- Hamburger or arrow icon -->
-             <span v-if="isSidebarCollapsed">&#x25BA;</span> <!-- Right arrow -->
+         <button
+           @click="toggleSidebar"
+           class="icon-button toggle-sidebar-button btn-touch"
+           aria-label="Toggle Sidebar"
+           :class="{ 'mobile-hamburger': isMobile }"
+         >
+             <!-- Mobile hamburger menu or desktop arrow -->
+             <span v-if="isMobile" class="hamburger-icon">
+               <span class="hamburger-line"></span>
+               <span class="hamburger-line"></span>
+               <span class="hamburger-line"></span>
+             </span>
+             <span v-else-if="isSidebarCollapsed">&#x25BA;</span> <!-- Right arrow -->
              <span v-else>&#x25C4;</span> <!-- Left arrow -->
          </button>
         <!-- App Logo/Name -->
-        <img src="/vite.svg" alt="App Logo" class="app-logo-small"> <!-- Assuming vite.svg is in public folder -->
-        <span v-if="!isSidebarCollapsed">Surgery Scheduler</span>
+        <img src="/vite.svg" alt="App Logo" class="app-logo-small">
+        <span v-if="!isSidebarCollapsed || !isMobile" class="app-title">Surgery Scheduler</span>
       </div>
-      <div class="global-search">
+      <div class="global-search" :class="{ 'mobile-hidden': isMobile && !showMobileSearch }">
         <!-- Global Search Bar -->
-        <input type="text" placeholder="Search..." v-model="searchTerm" @input="handleSearch" aria-label="Search">
+        <input
+          type="text"
+          placeholder="Search..."
+          v-model="searchTerm"
+          @input="handleSearch"
+          aria-label="Search"
+          class="form-control-mobile"
+        >
       </div>
       <div class="user-utilities">
+        <!-- Mobile Search Toggle -->
+        <button
+          v-if="isMobile"
+          @click="toggleMobileSearch"
+          class="icon-button btn-touch mobile-search-toggle"
+          aria-label="Toggle Search"
+        >
+          ≡ƒöì
+        </button>
         <!-- Notification Icon -->
-        <button class="icon-button" aria-label="Notifications">≡ƒöö</button>
+        <button class="icon-button btn-touch" aria-label="Notifications">
+          <span class="notification-icon">≡ƒöö</span>
+          <span v-if="notificationCount > 0" class="notification-badge">{{ notificationCount }}</span>
+        </button>
         <!-- User Profile Dropdown -->
-        <div class="user-profile" aria-haspopup="true" aria-expanded="false"> <!-- Add ARIA for dropdown -->
-          <span>{{ authStore.user?.username || 'User Name' }}</span> <!-- Display dynamic username -->
-          <!-- Dropdown icon/button here -->
-           <span class="user-profile-dropdown-icon">Γû╝</span> <!-- Simple dropdown arrow -->
+        <div class="user-profile" aria-haspopup="true" aria-expanded="false">
+          <span class="user-name" :class="{ 'mobile-hidden': isMobile }">{{ authStore.user?.username || 'User Name' }}</span>
+          <span class="user-profile-dropdown-icon">Γû╝</span>
         </div>
       </div>
     </header>
 
-    <aside class="left-sidebar">
+    <!-- Mobile Search Overlay -->
+    <div v-if="isMobile && showMobileSearch" class="mobile-search-overlay">
+      <div class="mobile-search-container">
+        <input
+          type="text"
+          placeholder="Search surgeries, patients, staff..."
+          v-model="searchTerm"
+          @input="handleSearch"
+          aria-label="Mobile Search"
+          class="mobile-search-input form-control-mobile"
+          ref="mobileSearchInput"
+        >
+        <button @click="toggleMobileSearch" class="mobile-search-close btn-touch">Γ£ò</button>
+      </div>
+    </div>
+
+    <!-- Mobile Navigation Overlay -->
+    <div v-if="isMobile && isMobileNavOpen" class="mobile-nav-overlay" @click="closeMobileNav"></div>
+
+    <aside class="left-sidebar" :class="{ 'mobile-sidebar': isMobile }">
       <!-- Navigation Links -->
       <nav aria-label="Main Navigation">
         <ul>
-          <li><router-link to="/dashboard"><span class="nav-icon" aria-hidden="true">≡ƒÅá</span><span v-if="!isSidebarCollapsed" class="nav-text">Dashboard</span></router-link></li>
-          <li><router-link to="/scheduling"><span class="nav-icon" aria-hidden="true">≡ƒôà</span><span v-if="!isSidebarCollapsed" class="nav-text">Scheduling</span></router-link></li>
-          <li><router-link to="/resource-management"><span class="nav-icon" aria-hidden="true">≡ƒ¢á∩╕Å</span><span v-if="!isSidebarCollapsed" class="nav-text">Resource Management</span></router-link></li>
-          <li><router-link to="/sdst-data-management"><span class="nav-icon" aria-hidden="true">≡ƒôè</span><span v-if="!isSidebarCollapsed" class="nav-text">SDST Data Management</span></router-link></li>
-          <li><router-link to="/reporting-analytics"><span class="nav-icon" aria-hidden="true">≡ƒôê</span><span v-if="!isSidebarCollapsed" class="nav-text">Reporting & Analytics</span></router-link></li>
-          <li><router-link to="/notifications"><span class="nav-icon" aria-hidden="true">≡ƒöö</span><span v-if="!isSidebarCollapsed" class="nav-text">Notifications</span></router-link></li>
-          <li><router-link to="/administration"><span class="nav-icon" aria-hidden="true">ΓÜÖ∩╕Å</span><span v-if="!isSidebarCollapsed" class="nav-text">Administration</span></router-link></li>
-          <li><router-link to="/patient-management"><span class="nav-icon" aria-hidden="true">≡ƒæ¿ΓÇìΓÜò∩╕Å</span><span v-if="!isSidebarCollapsed" class="nav-text">Patient Management</span></router-link></li>
-          <li><router-link to="/my-profile-settings"><span class="nav-icon" aria-hidden="true">≡ƒæñ</span><span v-if="!isSidebarCollapsed" class="nav-text">My Profile / Settings</span></router-link></li>
-          <li><router-link to="/help-documentation"><span class="nav-icon" aria-hidden="true">Γ¥ô</span><span v-if="!isSidebarCollapsed" class="nav-text">Help / Documentation</span></router-link></li>
-          <li class="logout-item"><button @click="handleLogout" class="logout-button"><span class="nav-icon" aria-hidden="true">≡ƒÜ¬</span><span v-if="!isSidebarCollapsed" class="nav-text">Logout</span></button></li>
+          <li>
+            <router-link to="/dashboard" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒÅá</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Dashboard</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/scheduling" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒôà</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Surgery Scheduling</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/master-schedule" @click="handleNavClick" class="nav-link nav-link-prominent">
+              <span class="nav-icon" aria-hidden="true">≡ƒôè</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Master Schedule (Gantt)</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/resource-management" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒ¢á∩╕Å</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Resource Management</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/sdst-data-management" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒôè</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">SDST Data Management</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/reporting-analytics" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒôê</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Reporting & Analytics</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/optimization" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒÜÇ</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Optimization Engine</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/notifications" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒöö</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Notifications</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/administration" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">ΓÜÖ∩╕Å</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Administration</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/patient-management" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒæ¿ΓÇìΓÜò∩╕Å</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Patient Management</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/my-profile-settings" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒæñ</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">My Profile / Settings</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/help-documentation" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">Γ¥ô</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Help / Documentation</span>
+            </router-link>
+          </li>
+          <li>
+            <router-link to="/mobile-test" @click="handleNavClick" class="nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒô▒</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Mobile Test</span>
+            </router-link>
+          </li>
+          <li class="logout-item">
+            <button @click="handleLogout" class="logout-button nav-link">
+              <span class="nav-icon" aria-hidden="true">≡ƒÜ¬</span>
+              <span v-if="!isSidebarCollapsed || isMobile" class="nav-text">Logout</span>
+            </button>
+          </li>
         </ul>
       </nav>
     </aside>
@@ -56,47 +180,124 @@
 </template>
 
 <script setup>
-import { ref } from 'vue';
+import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
 import { useRouter } from 'vue-router';
-// import { setAuthenticated } from '../router'; // REMOVED
-import { useToast } from 'vue-toastification'; // Assuming you use this for notifications
+import { useToast } from 'vue-toastification';
 
 // Import the authentication store
 import { useAuthStore } from '@/stores/authStore';
 import { storeToRefs } from 'pinia';
 
 const router = useRouter();
-const authStore = useAuthStore(); // Get store instance
+const authStore = useAuthStore();
 
 // Use storeToRefs for reactive state from the store if needed in template directly
 const { isAuthenticated, user, isLoading, error } = storeToRefs(authStore);
 
-const isSidebarCollapsed = ref(false); // State for sidebar collapse
-const searchTerm = ref(''); // State for global search input
+// Mobile responsiveness state
+const windowWidth = ref(window.innerWidth);
+const isMobile = computed(() => windowWidth.value < 768);
+const isTablet = computed(() => windowWidth.value >= 768 && windowWidth.value < 1024);
+
+// Navigation state
+const isSidebarCollapsed = ref(false);
+const isMobileNavOpen = ref(false);
+const showMobileSearch = ref(false);
+const searchTerm = ref('');
+
+// Notification state
+const notificationCount = ref(3); // Mock notification count
+
+// Refs for mobile functionality
+const mobileSearchInput = ref(null);
 
+// Handle window resize for responsive behavior
+const handleResize = () => {
+  windowWidth.value = window.innerWidth;
+
+  // Auto-close mobile nav when switching to desktop
+  if (!isMobile.value && isMobileNavOpen.value) {
+    isMobileNavOpen.value = false;
+  }
+
+  // Auto-close mobile search when switching to desktop
+  if (!isMobile.value && showMobileSearch.value) {
+    showMobileSearch.value = false;
+  }
+};
+
+// Toggle sidebar/mobile navigation
 const toggleSidebar = () => {
-  isSidebarCollapsed.value = !isSidebarCollapsed.value;
+  if (isMobile.value) {
+    isMobileNavOpen.value = !isMobileNavOpen.value;
+    // Prevent body scroll when mobile nav is open
+    document.body.style.overflow = isMobileNavOpen.value ? 'hidden' : '';
+  } else {
+    isSidebarCollapsed.value = !isSidebarCollapsed.value;
+  }
+};
+
+// Close mobile navigation
+const closeMobileNav = () => {
+  isMobileNavOpen.value = false;
+  document.body.style.overflow = '';
+};
+
+// Handle navigation click (close mobile nav on mobile)
+const handleNavClick = () => {
+  if (isMobile.value) {
+    closeMobileNav();
+  }
 };
 
+// Toggle mobile search
+const toggleMobileSearch = async () => {
+  showMobileSearch.value = !showMobileSearch.value;
+
+  if (showMobileSearch.value) {
+    // Focus the search input after the overlay is rendered
+    await nextTick();
+    if (mobileSearchInput.value) {
+      mobileSearchInput.value.focus();
+    }
+  }
+};
+
+// Handle logout
 const handleLogout = () => {
   console.log('AppLayout: Handling logout click.');
-  authStore.logout(); // Call the logout action from the auth store
-  // The auth store will handle clearing state and redirecting
+  authStore.logout();
+  // Close mobile nav if open
+  if (isMobile.value) {
+    closeMobileNav();
+  }
 };
 
+// Handle search
 const handleSearch = () => {
-  // Placeholder for actual search logic
   console.log('Searching for:', searchTerm.value);
-  // In a real app, this would trigger a search action,
-  // potentially navigating to a search results page or filtering data.
+  // In a real app, this would trigger a search action
+  // Close mobile search after search on mobile
+  if (isMobile.value && showMobileSearch.value) {
+    showMobileSearch.value = false;
+  }
 };
 
-// Optional: Check auth state on mount to ensure consistency (though router guard should handle initial check)
-// onMounted(() => {
-//    if (!authStore.isAuthenticated && router.currentRoute.value.meta.requiresAuth) {
-//        router.push({ name: 'Login' });
-//    }
-// });
+// Lifecycle hooks
+onMounted(() => {
+  window.addEventListener('resize', handleResize);
+
+  // Set initial sidebar state based on screen size
+  if (isMobile.value) {
+    isSidebarCollapsed.value = true;
+  }
+});
+
+onUnmounted(() => {
+  window.removeEventListener('resize', handleResize);
+  // Clean up body overflow style
+  document.body.style.overflow = '';
+});
 
 </script>
 
@@ -137,6 +338,16 @@ const handleSearch = () => {
     grid-template-columns: var(--sidebar-width-collapsed) 1fr; /* Collapsed: Narrower Sidebar */
 }
 
+/* Mobile layout adjustments */
+.app-layout.is-mobile {
+  grid-template-columns: 1fr; /* Single column on mobile */
+  grid-template-rows: var(--top-nav-height) 1fr;
+}
+
+.app-layout.is-mobile.mobile-nav-open {
+  overflow: hidden;
+}
+
 .top-nav-bar {
   grid-column: 1 / 3; /* Span across both columns */
   grid-row: 1;
@@ -174,6 +385,39 @@ const handleSearch = () => {
     color: var(--color-primary);
 }
 
+/* Mobile hamburger menu styles */
+.mobile-hamburger {
+  position: relative;
+  padding: 12px;
+}
+
+.hamburger-icon {
+  display: flex;
+  flex-direction: column;
+  justify-content: space-between;
+  width: 20px;
+  height: 16px;
+}
+
+.hamburger-line {
+  width: 100%;
+  height: 2px;
+  background-color: var(--color-text-secondary);
+  transition: all 0.3s ease;
+}
+
+.mobile-nav-open .hamburger-line:nth-child(1) {
+  transform: rotate(45deg) translate(5px, 5px);
+}
+
+.mobile-nav-open .hamburger-line:nth-child(2) {
+  opacity: 0;
+}
+
+.mobile-nav-open .hamburger-line:nth-child(3) {
+  transform: rotate(-45deg) translate(7px, -6px);
+}
+
 .app-logo-small {
     height: 32px;
     margin-right: 10px;
@@ -221,6 +465,7 @@ const handleSearch = () => {
     color: var(--color-text-secondary);
     border-radius: 50%;
     transition: background-color 0.2s ease, color 0.2s ease;
+    position: relative;
 }
 
 .icon-button:hover {
@@ -228,6 +473,63 @@ const handleSearch = () => {
     color: var(--color-primary);
 }
 
+/* Notification badge */
+.notification-badge {
+  position: absolute;
+  top: 2px;
+  right: 2px;
+  background-color: var(--color-danger);
+  color: white;
+  border-radius: 50%;
+  width: 18px;
+  height: 18px;
+  font-size: 0.7em;
+  display: flex;
+  align-items: center;
+  justify-content: center;
+  font-weight: var(--font-weight-bold);
+}
+
+/* Mobile search overlay */
+.mobile-search-overlay {
+  position: fixed;
+  top: var(--top-nav-height);
+  left: 0;
+  right: 0;
+  background-color: var(--color-surface);
+  border-bottom: 1px solid var(--color-border);
+  z-index: var(--z-index-mobile-nav);
+  padding: var(--spacing-md);
+  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
+}
+
+.mobile-search-container {
+  display: flex;
+  gap: var(--spacing-sm);
+  align-items: center;
+}
+
+.mobile-search-input {
+  flex: 1;
+  padding: var(--spacing-sm) var(--spacing-md);
+  border: 1px solid var(--color-border);
+  border-radius: var(--border-radius-md);
+  font-size: var(--font-size-base);
+}
+
+.mobile-search-close {
+  background-color: var(--color-background-mute);
+  border: 1px solid var(--color-border);
+  border-radius: 50%;
+  width: var(--touch-target-comfortable);
+  height: var(--touch-target-comfortable);
+  display: flex;
+  align-items: center;
+  justify-content: center;
+  font-size: var(--font-size-lg);
+  color: var(--color-text-secondary);
+}
+
 .user-profile {
     display: flex;
     align-items: center;
@@ -267,6 +569,34 @@ const handleSearch = () => {
   border-right: 1px solid var(--color-border);
 }
 
+/* Mobile sidebar styles */
+.left-sidebar.mobile-sidebar {
+  position: fixed;
+  top: var(--top-nav-height);
+  left: 0;
+  bottom: 0;
+  width: 280px;
+  z-index: var(--z-index-mobile-nav);
+  transform: translateX(-100%);
+  transition: transform 0.3s ease-in-out;
+  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
+}
+
+.app-layout.mobile-nav-open .left-sidebar.mobile-sidebar {
+  transform: translateX(0);
+}
+
+/* Mobile navigation overlay */
+.mobile-nav-overlay {
+  position: fixed;
+  top: var(--top-nav-height);
+  left: 0;
+  right: 0;
+  bottom: 0;
+  background-color: rgba(0, 0, 0, 0.5);
+  z-index: calc(var(--z-index-mobile-nav) - 1);
+}
+
 .left-sidebar nav ul {
   list-style: none;
   padding: 0;
@@ -278,7 +608,8 @@ const handleSearch = () => {
 }
 
 .left-sidebar nav a,
-.left-sidebar nav .logout-button {
+.left-sidebar nav .logout-button,
+.nav-link {
   display: flex;
   align-items: center;
   padding: 12px 20px;
@@ -290,6 +621,14 @@ const handleSearch = () => {
   white-space: nowrap;
   overflow: hidden;
   border-left: 3px solid transparent;
+  min-height: var(--touch-target-min);
+}
+
+/* Mobile navigation link styles */
+.mobile-sidebar .nav-link {
+  padding: 16px 20px;
+  font-size: 1rem;
+  min-height: var(--touch-target-comfortable);
 }
 
 .app-layout.sidebar-collapsed .left-sidebar nav a,
@@ -314,6 +653,27 @@ const handleSearch = () => {
   border-left-color: var(--color-primary);
 }
 
+/* Prominent navigation link styling for Master Schedule */
+.nav-link-prominent {
+  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
+  color: white !important;
+  font-weight: 600;
+  border-left: 3px solid var(--color-primary-dark);
+  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
+}
+
+.nav-link-prominent:hover {
+  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
+  color: white !important;
+  transform: translateX(2px);
+}
+
+.nav-link-prominent.router-link-exact-active {
+  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
+  color: white !important;
+  border-left-color: white;
+}
+
 .nav-icon {
     margin-right: 12px;
     font-size: 1.2em;
@@ -377,4 +737,53 @@ const handleSearch = () => {
 .main-content::-webkit-scrollbar-track {
   background-color: transparent;
 }
+
+/* Mobile main content styles */
+.app-layout.is-mobile .main-content {
+  grid-column: 1;
+  padding: var(--spacing-md);
+  padding-bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom, 0px));
+}
+
+/* Responsive breakpoints for AppLayout */
+@media (max-width: 767px) {
+  .app-brand .app-title {
+    font-size: var(--font-size-base);
+  }
+
+  .global-search {
+    display: none;
+  }
+
+  .user-utilities .user-name {
+    display: none;
+  }
+
+  .icon-button {
+    margin-left: var(--spacing-sm);
+    padding: var(--spacing-sm);
+  }
+}
+
+@media (max-width: 480px) {
+  .top-nav-bar {
+    padding: 0 var(--spacing-sm);
+  }
+
+  .app-brand .app-title {
+    display: none;
+  }
+
+  .main-content {
+    padding: var(--spacing-sm);
+  }
+}
+
+/* Landscape orientation adjustments for mobile */
+@media (max-height: 500px) and (orientation: landscape) {
+  .mobile-sidebar .nav-link {
+    padding: 12px 20px;
+    min-height: 40px;
+  }
+}
 </style>
diff --git a/src/components/DashboardScreen.vue b/src/components/DashboardScreen.vue
index e9b8486..d25621a 100644
--- a/src/components/DashboardScreen.vue
+++ b/src/components/DashboardScreen.vue
@@ -1,52 +1,146 @@
 <template>
   <div class="dashboard-container">
-    <h1>Welcome, {{ authStore.user?.username || 'User' }}!</h1>
+    <!-- Enhanced Header Section -->
+    <div class="dashboard-header">
+      <div class="welcome-section">
+        <h1>Welcome back, {{ authStore.user?.username || 'User' }}!</h1>
+        <p class="welcome-subtitle">{{ getCurrentDateString() }} ΓÇó {{ getCurrentTimeString() }}</p>
+      </div>
+      <div class="header-stats">
+        <div class="header-stat-item">
+          <div class="stat-icon">≡ƒÅÑ</div>
+          <div class="stat-info">
+            <div class="stat-value">{{ totalORsActive }}</div>
+            <div class="stat-label">Active ORs</div>
+          </div>
+        </div>
+        <div class="header-stat-item">
+          <div class="stat-icon">ΓÅ▒∩╕Å</div>
+          <div class="stat-info">
+            <div class="stat-value">{{ upcomingSurgeries }}</div>
+            <div class="stat-label">Upcoming Today</div>
+          </div>
+        </div>
+        <div class="header-stat-item urgent" v-if="urgentAlerts > 0">
+          <div class="stat-icon">≡ƒÜ¿</div>
+          <div class="stat-info">
+            <div class="stat-value">{{ urgentAlerts }}</div>
+            <div class="stat-label">Urgent Alerts</div>
+          </div>
+        </div>
+      </div>
+    </div>
 
     <div v-if="scheduleStore.isLoading" class="loading-message">
-      Loading dashboard data...
+      <div class="loading-spinner"></div>
+      <p>Loading dashboard data...</p>
     </div>
 
     <div v-else class="dashboard-widgets">
-      <!-- Quick Actions Widget -->
+      <!-- Enhanced Quick Actions Widget -->
       <div class="widget quick-actions-widget">
-        <h2>Quick Actions</h2>
-        <div class="quick-action-buttons">
-          <button @click="scheduleNewSurgery">Schedule New Elective Surgery</button>
-          <button class="btn btn-secondary" @click="addEmergencyCase">Add Emergency Case</button>
-          <button @click="goToMasterSchedule">Go to Master Schedule</button>
-          <button @click="manageResources">Manage Resources</button>
-          <button class="btn btn-secondary" @click="runOptimization">Run Optimization</button>
+        <div class="widget-header">
+          <h2>Quick Actions</h2>
+          <span class="widget-subtitle">Common tasks and shortcuts</span>
+        </div>
+        <div class="quick-action-grid">
+          <button @click="scheduleNewSurgery" class="action-card primary">
+            <div class="action-icon">≡ƒôà</div>
+            <div class="action-content">
+              <div class="action-title">Schedule Surgery</div>
+              <div class="action-description">Add new elective surgery</div>
+            </div>
+          </button>
+          <button @click="addEmergencyCase" class="action-card emergency">
+            <div class="action-icon">≡ƒÜ¿</div>
+            <div class="action-content">
+              <div class="action-title">Emergency Case</div>
+              <div class="action-description">Add urgent surgery</div>
+            </div>
+          </button>
+          <button @click="goToMasterSchedule" class="action-card featured">
+            <div class="action-icon">≡ƒôè</div>
+            <div class="action-content">
+              <div class="action-title">Master Schedule</div>
+              <div class="action-description">View Gantt chart</div>
+            </div>
+          </button>
+          <button @click="manageResources" class="action-card secondary">
+            <div class="action-icon">≡ƒ¢á∩╕Å</div>
+            <div class="action-content">
+              <div class="action-title">Manage Resources</div>
+              <div class="action-description">ORs, staff, equipment</div>
+            </div>
+          </button>
+          <button @click="runOptimization" class="action-card optimization">
+            <div class="action-icon">≡ƒÜÇ</div>
+            <div class="action-content">
+              <div class="action-title">Run Optimization</div>
+              <div class="action-description">Optimize schedule</div>
+            </div>
+          </button>
+          <button @click="viewReports" class="action-card secondary">
+            <div class="action-icon">≡ƒôê</div>
+            <div class="action-content">
+              <div class="action-title">View Reports</div>
+              <div class="action-description">Analytics & insights</div>
+            </div>
+          </button>
         </div>
       </div>
 
-      <!-- Key Performance Indicators (KPIs) Widget -->
+      <!-- Enhanced KPIs Widget -->
       <div class="widget kpis-widget">
-        <h2>Key Performance Indicators</h2>
-        <div class="kpi-list">
-          <!-- KPIs will likely come from the scheduleStore or a dedicated reporting store later -->
-          <div class="kpi-item" @click="navigateToReport('OR Utilization')">
-            <span class="kpi-label">OR Utilization (Today):</span>
-            <!-- Using simulated data for now -->
-            <span class="kpi-value">{{ orUtilizationToday }}%</span>
+        <div class="widget-header">
+          <h2>Performance Dashboard</h2>
+          <span class="widget-subtitle">Real-time metrics and insights</span>
+        </div>
+        <div class="kpi-grid">
+          <div class="kpi-card" :class="getKPIClass('utilization', orUtilizationToday)">
+            <div class="kpi-icon">≡ƒÅÑ</div>
+            <div class="kpi-content">
+              <div class="kpi-value">{{ orUtilizationToday }}%</div>
+              <div class="kpi-label">OR Utilization</div>
+              <div class="kpi-trend" :class="getTrendClass('utilization')">
+                <span class="trend-icon">{{ getTrendIcon('utilization') }}</span>
+                <span class="trend-text">{{ getTrendText('utilization') }}</span>
+              </div>
+            </div>
           </div>
-          <div class="kpi-item" @click="navigateToReport('Avg. SDST')">
-            <span class="kpi-label">Avg. SDST (Today):</span>
-             <!-- Using simulated data for now -->
-            <span class="kpi-value">{{ avgSdstToday }} min</span>
+          <div class="kpi-card" :class="getKPIClass('sdst', avgSdstToday)">
+            <div class="kpi-icon">ΓÅ▒∩╕Å</div>
+            <div class="kpi-content">
+              <div class="kpi-value">{{ avgSdstToday }}m</div>
+              <div class="kpi-label">Average SDST</div>
+              <div class="kpi-trend" :class="getTrendClass('sdst')">
+                <span class="trend-icon">{{ getTrendIcon('sdst') }}</span>
+                <span class="trend-text">{{ getTrendText('sdst') }}</span>
+              </div>
+            </div>
           </div>
-          <div class="kpi-item" @click="navigateToReport('Emergency Cases')">
-            <span class="kpi-label">Emergency Cases (Today):</span>
-             <!-- Using simulated data for now -->
-            <span class="kpi-value">{{ emergencyCasesToday }}</span>
+          <div class="kpi-card" :class="getKPIClass('emergency', emergencyCasesToday)">
+            <div class="kpi-icon">≡ƒÜ¿</div>
+            <div class="kpi-content">
+              <div class="kpi-value">{{ emergencyCasesToday }}</div>
+              <div class="kpi-label">Emergency Cases</div>
+              <div class="kpi-trend" :class="getTrendClass('emergency')">
+                <span class="trend-icon">{{ getTrendIcon('emergency') }}</span>
+                <span class="trend-text">{{ getTrendText('emergency') }}</span>
+              </div>
+            </div>
           </div>
-          <div class="kpi-item" @click="navigateToReport('Cancelled Surgeries')">
-            <span class="kpi-label">Cancelled Surgeries (Today):</span>
-             <!-- Using simulated data for now -->
-            <span class="kpi-value">{{ cancelledSurgeriesToday }}</span>
+          <div class="kpi-card" :class="getKPIClass('cancelled', cancelledSurgeriesToday)">
+            <div class="kpi-icon">Γ¥î</div>
+            <div class="kpi-content">
+              <div class="kpi-value">{{ cancelledSurgeriesToday }}</div>
+              <div class="kpi-label">Cancelled Today</div>
+              <div class="kpi-trend" :class="getTrendClass('cancelled')">
+                <span class="trend-icon">{{ getTrendIcon('cancelled') }}</span>
+                <span class="trend-text">{{ getTrendText('cancelled') }}</span>
+              </div>
+            </div>
           </div>
         </div>
-        <!-- Placeholder for charts/visualizations -->
-        <p><em>(Placeholder for KPI charts/visualizations)</em></p>
       </div>
 
       <!-- Today's OR Schedule Overview Widget -->
@@ -198,8 +292,8 @@ const addEmergencyCase = () => {
 };
 
 const goToMasterSchedule = () => {
-  console.log('Navigate to Master Schedule');
-  router.push({ name: 'Scheduling' });
+  console.log('Navigate to Master Schedule (Gantt Chart)');
+  router.push({ name: 'MasterSchedule' });
 };
 
 const manageResources = () => {
@@ -214,8 +308,122 @@ const runOptimization = () => {
   // If it navigates, assuming a route named 'OptimizationControl'
   // router.push({ name: 'OptimizationControl' });
 };
+
+const viewReports = () => {
+  console.log('Navigate to Reports and Analytics');
+  // router.push({ name: 'ReportsAnalytics' });
+};
 // -------------------------------------
 
+// --- Enhanced Dashboard Methods ---
+const getCurrentDateString = () => {
+  return new Date().toLocaleDateString('en-US', {
+    weekday: 'long',
+    year: 'numeric',
+    month: 'long',
+    day: 'numeric'
+  });
+};
+
+const getCurrentTimeString = () => {
+  return new Date().toLocaleTimeString('en-US', {
+    hour: '2-digit',
+    minute: '2-digit'
+  });
+};
+
+// Computed properties for header stats
+const totalORsActive = computed(() => {
+  // Count ORs that have surgeries scheduled today
+  const today = new Date().toDateString();
+  const activeORs = new Set();
+
+  visibleScheduledSurgeries.value.forEach(surgery => {
+    const surgeryDate = new Date(surgery.startTime).toDateString();
+    if (surgeryDate === today) {
+      activeORs.add(surgery.orName);
+    }
+  });
+
+  return activeORs.size;
+});
+
+const upcomingSurgeries = computed(() => {
+  const now = new Date();
+  const endOfDay = new Date(now);
+  endOfDay.setHours(23, 59, 59, 999);
+
+  return visibleScheduledSurgeries.value.filter(surgery => {
+    const surgeryTime = new Date(surgery.startTime);
+    return surgeryTime > now && surgeryTime <= endOfDay;
+  }).length;
+});
+
+const urgentAlerts = computed(() => {
+  return criticalAlerts.value.length + sdstConflictsFromStore.value.length;
+});
+
+// KPI Enhancement Methods
+const getKPIClass = (type, value) => {
+  switch (type) {
+    case 'utilization':
+      if (value >= 85) return 'excellent';
+      if (value >= 70) return 'good';
+      if (value >= 50) return 'warning';
+      return 'poor';
+    case 'sdst':
+      if (value <= 20) return 'excellent';
+      if (value <= 30) return 'good';
+      if (value <= 45) return 'warning';
+      return 'poor';
+    case 'emergency':
+      if (value <= 1) return 'excellent';
+      if (value <= 3) return 'good';
+      if (value <= 5) return 'warning';
+      return 'poor';
+    case 'cancelled':
+      if (value === 0) return 'excellent';
+      if (value <= 2) return 'good';
+      if (value <= 4) return 'warning';
+      return 'poor';
+    default:
+      return 'good';
+  }
+};
+
+const getTrendClass = (type) => {
+  // Simulated trend data - in real app, this would come from historical data
+  const trends = {
+    utilization: 'up',
+    sdst: 'down',
+    emergency: 'stable',
+    cancelled: 'down'
+  };
+  return trends[type] || 'stable';
+};
+
+const getTrendIcon = (type) => {
+  const trendClass = getTrendClass(type);
+  switch (trendClass) {
+    case 'up': return 'Γåù∩╕Å';
+    case 'down': return 'Γåÿ∩╕Å';
+    case 'stable': return 'ΓåÆ';
+    default: return 'ΓåÆ';
+  }
+};
+
+const getTrendText = (type) => {
+  const trendClass = getTrendClass(type);
+  const improvements = {
+    utilization: { up: '+5% vs yesterday', down: '-3% vs yesterday', stable: 'No change' },
+    sdst: { up: '+2min vs avg', down: '-5min vs avg', stable: 'Within range' },
+    emergency: { up: '+1 vs yesterday', down: '-1 vs yesterday', stable: 'Normal level' },
+    cancelled: { up: '+1 vs yesterday', down: '-2 vs yesterday', stable: 'No change' }
+  };
+
+  return improvements[type]?.[trendClass] || 'No data';
+};
+
 // --- KPI Click Handler ---
 const navigateToReport = (kpiName) => {
   console.log(`Navigating to report for: ${kpiName}`);
@@ -296,7 +504,115 @@ onMounted(() => {
 */
 
 .dashboard-container {
-  padding: var(--spacing-md); /* Use global spacing variable */
+  padding: var(--spacing-lg);
+  background-color: var(--color-background);
+  min-height: 100vh;
+}
+
+/* Enhanced Dashboard Header */
+.dashboard-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: flex-start;
+  margin-bottom: var(--spacing-xl);
+  padding: var(--spacing-xl);
+  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
+  color: white;
+  border-radius: var(--border-radius-lg);
+  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
+}
+
+.welcome-section h1 {
+  margin: 0 0 var(--spacing-xs) 0;
+  font-size: var(--font-size-xxl);
+  font-weight: var(--font-weight-bold);
+}
+
+.welcome-subtitle {
+  margin: 0;
+  font-size: var(--font-size-base);
+  opacity: 0.9;
+  font-weight: var(--font-weight-normal);
+}
+
+.header-stats {
+  display: flex;
+  gap: var(--spacing-lg);
+  flex-wrap: wrap;
+}
+
+.header-stat-item {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-sm);
+  background: rgba(255, 255, 255, 0.1);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-md);
+  backdrop-filter: blur(10px);
+  border: 1px solid rgba(255, 255, 255, 0.2);
+  transition: all 0.3s ease;
+}
+
+.header-stat-item:hover {
+  background: rgba(255, 255, 255, 0.2);
+  transform: translateY(-2px);
+}
+
+.header-stat-item.urgent {
+  background: rgba(220, 53, 69, 0.2);
+  border-color: rgba(220, 53, 69, 0.3);
+  animation: pulse 2s infinite;
+}
+
+@keyframes pulse {
+  0%, 100% { opacity: 1; }
+  50% { opacity: 0.7; }
+}
+
+.stat-icon {
+  font-size: var(--font-size-xl);
+}
+
+.stat-info {
+  text-align: center;
+}
+
+.stat-value {
+  font-size: var(--font-size-lg);
+  font-weight: var(--font-weight-bold);
+  line-height: 1;
+  margin-bottom: var(--spacing-xs);
+}
+
+.stat-label {
+  font-size: var(--font-size-sm);
+  opacity: 0.9;
+  font-weight: var(--font-weight-medium);
+}
+
+.loading-message {
+  display: flex;
+  flex-direction: column;
+  align-items: center;
+  justify-content: center;
+  padding: var(--spacing-xl);
+  text-align: center;
+  color: var(--color-text-secondary);
+}
+
+.loading-spinner {
+  width: 40px;
+  height: 40px;
+  border: 4px solid var(--color-border);
+  border-top: 4px solid var(--color-primary);
+  border-radius: 50%;
+  animation: spin 1s linear infinite;
+  margin-bottom: var(--spacing-md);
+}
+
+@keyframes spin {
+  0% { transform: rotate(0deg); }
+  100% { transform: rotate(360deg); }
 }
 
 .dashboard-widgets {
@@ -324,57 +640,230 @@ onMounted(() => {
   color: var(--color-very-dark-gray); /* Use global text color variable */
 }
 
-/* Quick Actions Widget Specific Styles */
-.quick-actions-widget .quick-action-buttons,
-.conflict-details-widget .conflict-actions {
+/* Enhanced Widget Headers */
+.widget-header {
+  margin-bottom: var(--spacing-lg);
+}
+
+.widget-header h2 {
+  margin-bottom: var(--spacing-xs);
+  border-bottom: none;
+  padding-bottom: 0;
+}
+
+.widget-subtitle {
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+  font-weight: var(--font-weight-normal);
+}
+
+/* Enhanced Quick Actions Widget */
+.quick-action-grid {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
+  gap: var(--spacing-md);
+}
+
+.action-card {
   display: flex;
-  flex-wrap: wrap;
-  gap: var(--spacing-sm); /* Use global spacing variable */
+  align-items: center;
+  gap: var(--spacing-md);
+  padding: var(--spacing-lg);
+  border: none;
+  border-radius: var(--border-radius-md);
+  background: var(--color-surface);
+  color: var(--color-text);
+  text-align: left;
+  cursor: pointer;
+  transition: all 0.3s ease;
+  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
+  border-left: 4px solid transparent;
+}
+
+.action-card:hover {
+  transform: translateY(-2px);
+  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
+}
+
+.action-card.primary {
+  border-left-color: var(--color-primary);
 }
 
-.quick-actions-widget button,
-.conflict-details-widget button {
-  /* Inherits base button styles from global style.css */
-  /* You can add minor overrides here if needed */
+.action-card.primary:hover {
+  background: var(--color-primary-light);
+  color: white;
 }
 
-.quick-actions-widget .btn-secondary,
-.conflict-details-widget .btn-secondary {
-    /* Inherits .btn-secondary styles from global style.css */
+.action-card.emergency {
+  border-left-color: var(--color-danger);
 }
 
+.action-card.emergency:hover {
+  background: var(--color-danger);
+  color: white;
+}
+
+.action-card.featured {
+  border-left-color: var(--color-success);
+  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
+  color: white;
+}
+
+.action-card.featured:hover {
+  background: linear-gradient(135deg, var(--color-success-dark) 0%, var(--color-success) 100%);
+}
+
+.action-card.secondary {
+  border-left-color: var(--color-secondary);
+}
+
+.action-card.secondary:hover {
+  background: var(--color-secondary);
+  color: white;
+}
+
+.action-card.optimization {
+  border-left-color: var(--color-warning);
+}
+
+.action-card.optimization:hover {
+  background: var(--color-warning);
+  color: white;
+}
+
+.action-icon {
+  font-size: var(--font-size-xl);
+  flex-shrink: 0;
+}
+
+.action-content {
+  flex: 1;
+}
+
+.action-title {
+  font-size: var(--font-size-base);
+  font-weight: var(--font-weight-semibold);
+  margin-bottom: var(--spacing-xs);
+}
+
+.action-description {
+  font-size: var(--font-size-sm);
+  opacity: 0.8;
+}
 
-/* KPI Widget Specific Styles */
-.kpi-list {
+
+/* Enhanced KPI Widget Styles */
+.kpi-grid {
   display: grid;
-  grid-template-columns: 1fr 1fr; /* Two columns for KPI items */
-  gap: var(--spacing-sm); /* Use global spacing variable */
+  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
+  gap: var(--spacing-md);
 }
 
-.kpi-item {
-  text-align: left;
+.kpi-card {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-md);
+  padding: var(--spacing-lg);
+  background: var(--color-surface);
+  border-radius: var(--border-radius-md);
+  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
+  transition: all 0.3s ease;
+  border-left: 4px solid var(--color-border);
   cursor: pointer;
-  transition: background-color 0.2s ease;
-  padding: var(--spacing-sm); /* Use global spacing variable */
-  border-radius: var(--border-radius-sm); /* Use global border radius variable */
 }
 
-.kpi-item:hover {
-  background-color: var(--color-background-soft); /* Use global background variable */
+.kpi-card:hover {
+  transform: translateY(-2px);
+  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
 }
 
-.kpi-item .kpi-label {
-  display: block;
-  font-size: var(--font-size-sm); /* Use global font size variable */
-  color: var(--color-dark-gray); /* Use global text color variable */
-  margin-bottom: var(--spacing-xs); /* Use global spacing variable */
+.kpi-card.excellent {
+  border-left-color: var(--color-success);
+  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.02) 100%);
+}
+
+.kpi-card.good {
+  border-left-color: var(--color-primary);
+  background: linear-gradient(135deg, rgba(0, 117, 194, 0.05) 0%, rgba(0, 117, 194, 0.02) 100%);
+}
+
+.kpi-card.warning {
+  border-left-color: var(--color-warning);
+  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0.02) 100%);
+}
+
+.kpi-card.poor {
+  border-left-color: var(--color-danger);
+  background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.02) 100%);
 }
 
-.kpi-item .kpi-value {
-  font-size: var(--font-size-xl); /* Use global font size variable */
-  font-weight: var(--font-weight-bold); /* Use global font weight variable */
-  color: var(--color-primary); /* Use global primary color variable */
-  line-height: 1.2;
+.kpi-icon {
+  font-size: var(--font-size-xxl);
+  flex-shrink: 0;
+}
+
+.kpi-content {
+  flex: 1;
+}
+
+.kpi-value {
+  font-size: var(--font-size-xxl);
+  font-weight: var(--font-weight-bold);
+  line-height: 1;
+  margin-bottom: var(--spacing-xs);
+}
+
+.kpi-card.excellent .kpi-value {
+  color: var(--color-success);
+}
+
+.kpi-card.good .kpi-value {
+  color: var(--color-primary);
+}
+
+.kpi-card.warning .kpi-value {
+  color: var(--color-warning);
+}
+
+.kpi-card.poor .kpi-value {
+  color: var(--color-danger);
+}
+
+.kpi-label {
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+  font-weight: var(--font-weight-medium);
+  text-transform: uppercase;
+  letter-spacing: 0.5px;
+  margin-bottom: var(--spacing-xs);
+}
+
+.kpi-trend {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-xs);
+  font-size: var(--font-size-xs);
+  font-weight: var(--font-weight-medium);
+}
+
+.kpi-trend.up {
+  color: var(--color-success);
+}
+
+.kpi-trend.down {
+  color: var(--color-danger);
+}
+
+.kpi-trend.stable {
+  color: var(--color-text-secondary);
+}
+
+.trend-icon {
+  font-size: var(--font-size-sm);
+}
+
+.trend-text {
+  opacity: 0.8;
 }
 
 /* Specific widget adjustments */
@@ -461,4 +950,134 @@ onMounted(() => {
     padding-top: var(--spacing-md); /* Space above action buttons */
      border-top: 1px solid var(--color-border-soft);
 }
+
+/* Mobile Responsiveness */
+@media (max-width: 768px) {
+  .dashboard-container {
+    padding: var(--spacing-md);
+  }
+
+  .dashboard-header {
+    flex-direction: column;
+    gap: var(--spacing-lg);
+    text-align: center;
+    padding: var(--spacing-lg);
+  }
+
+  .welcome-section h1 {
+    font-size: var(--font-size-xl);
+  }
+
+  .header-stats {
+    justify-content: center;
+    gap: var(--spacing-md);
+  }
+
+  .header-stat-item {
+    flex-direction: column;
+    text-align: center;
+    padding: var(--spacing-sm);
+    min-width: 80px;
+  }
+
+  .stat-icon {
+    font-size: var(--font-size-lg);
+  }
+
+  .stat-value {
+    font-size: var(--font-size-base);
+  }
+
+  .stat-label {
+    font-size: var(--font-size-xs);
+  }
+
+  .dashboard-widgets {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-md);
+  }
+
+  .quick-action-grid {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-sm);
+  }
+
+  .action-card {
+    padding: var(--spacing-md);
+  }
+
+  .action-icon {
+    font-size: var(--font-size-lg);
+  }
+
+  .action-title {
+    font-size: var(--font-size-sm);
+  }
+
+  .action-description {
+    font-size: var(--font-size-xs);
+  }
+
+  .kpi-grid {
+    grid-template-columns: 1fr;
+    gap: var(--spacing-sm);
+  }
+
+  .kpi-card {
+    padding: var(--spacing-md);
+  }
+
+  .kpi-icon {
+    font-size: var(--font-size-xl);
+  }
+
+  .kpi-value {
+    font-size: var(--font-size-xl);
+  }
+
+  .kpi-label {
+    font-size: var(--font-size-xs);
+  }
+}
+
+@media (max-width: 480px) {
+  .dashboard-header {
+    padding: var(--spacing-md);
+  }
+
+  .welcome-section h1 {
+    font-size: var(--font-size-lg);
+  }
+
+  .welcome-subtitle {
+    font-size: var(--font-size-sm);
+  }
+
+  .header-stats {
+    gap: var(--spacing-sm);
+  }
+
+  .header-stat-item {
+    padding: var(--spacing-xs);
+    min-width: 60px;
+  }
+
+  .kpi-grid {
+    grid-template-columns: repeat(2, 1fr);
+  }
+
+  .kpi-card {
+    flex-direction: column;
+    text-align: center;
+    padding: var(--spacing-sm);
+  }
+
+  .kpi-icon {
+    font-size: var(--font-size-lg);
+  }
+
+  .kpi-value {
+    font-size: var(--font-size-lg);
+  }
+}
 </style>
diff --git a/src/components/GanttChart.vue b/src/components/GanttChart.vue
index b708eb0..aa6a5b0 100644
--- a/src/components/GanttChart.vue
+++ b/src/components/GanttChart.vue
@@ -59,7 +59,14 @@
     <div class="gantt-grid" ref="ganttGrid">
       <div class="gantt-time-axis">
         <!-- Hourly markers (adjust based on view mode) -->
-        <div v-for="hour in hours" :key="hour" class="time-marker">{{ formatHourMarker(hour) }}</div>
+        <div
+          v-for="hour in hours"
+          :key="hour"
+          class="time-marker"
+          :style="getTimeMarkerStyle(hour)"
+        >
+          {{ formatHourMarker(hour) }}
+        </div>
       </div>
       <div class="gantt-or-rows">
         <div
@@ -304,6 +311,38 @@ const formatHourMarker = (hour) => {
   return '';
 };
 
+// Calculate time marker positioning
+const getTimeMarkerStyle = (hour) => {
+  if (!currentDateRange || !currentDateRange.start) {
+    return { left: '0px', width: '100px' };
+  }
+
+  if (scheduleStore.ganttViewMode === 'Day') {
+    // For day view, position based on hour
+    const viewStartHour = currentDateRange.start.getHours();
+    const hourOffset = hour - viewStartHour;
+    const leftPosition = hourOffset * 60 * pixelsPerMinute.value; // 60 minutes per hour
+
+    return {
+      left: `${leftPosition}px`,
+      width: `${60 * pixelsPerMinute.value}px`, // Width of one hour
+      minWidth: '60px'
+    };
+  } else if (scheduleStore.ganttViewMode === 'Week') {
+    // For week view, position based on day
+    const daysDiff = Math.floor((hour.getTime() - currentDateRange.start.getTime()) / (1000 * 60 * 60 * 24));
+    const leftPosition = daysDiff * 24 * 60 * pixelsPerMinute.value; // 24 hours per day
+
+    return {
+      left: `${leftPosition}px`,
+      width: `${24 * 60 * pixelsPerMinute.value}px`, // Width of one day
+      minWidth: '100px'
+    };
+  }
+
+  return { left: '0px', width: '100px' };
+};
+
 // Get surgeries for a specific OR within the current view, sorted by time
 const getSurgeriesForOR = (orId) => {
   return scheduleStore.getSurgeriesForOR(orId);
@@ -444,6 +483,34 @@ const formatTime = (dateString) => {
   return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
 };
 
+// Get SDST explanation for tooltip
+const getSDSTExplanation = (surgery) => {
+  if (!surgery || !surgery.sdsTime) return 'No setup time required';
+
+  const precedingType = surgery.precedingType || 'Initial';
+  const currentType = surgery.type;
+
+  if (precedingType === 'Initial') {
+    return `Initial setup for ${currentType} surgery`;
+  } else {
+    return `Transition from ${precedingType} to ${currentType} surgery`;
+  }
+};
+
+// Get accessible label for surgery blocks
+const getSurgeryAccessibleLabel = (surgery) => {
+  const conflicts = surgery.conflicts && surgery.conflicts.length > 0
+    ? ` Warning: ${surgery.conflicts.length} conflict(s)`
+    : '';
+
+  return `Surgery: ${surgery.patientName}, Type: ${surgery.fullType}, Time: ${formatTime(surgery.startTime)} to ${formatTime(surgery.endTime)}, SDST: ${surgery.sdsTime} minutes${conflicts}`;
+};
+
+// Real-time SDST calculation and conflict detection using store method
+const calculateSDSTAndConflicts = (surgery, targetORId, proposedStartTime) => {
+  return scheduleStore.calculateSDSTForPosition(surgery, targetORId, proposedStartTime);
+};
+
 // Navigation and view controls
 const navigateDateRange = (direction) => {
   scheduleStore.navigateGanttDate(direction);
@@ -487,15 +554,38 @@ const onDragOver = (event, orId) => {
   event.preventDefault();
   event.dataTransfer.dropEffect = 'move';
 
-  // Update ghost position during drag
+  // Update ghost position and calculate real-time SDST during drag
   if (dragGhost.value.visible) {
     const ganttTimeline = event.target.closest('.or-timeline');
     if (ganttTimeline) {
       const timelineRect = ganttTimeline.getBoundingClientRect();
       const clickX = event.clientX - timelineRect.left;
 
-      // Update ghost position for visual feedback
-      // This would be implemented in a real app
+      // Calculate proposed time based on cursor position
+      const minutesFromViewStart = clickX / pixelsPerMinute.value;
+      const proposedStartTime = new Date(currentDateRange.start.getTime() + minutesFromViewStart * 60 * 1000);
+
+      // Get the dragged surgery data
+      const draggedSurgeryId = event.dataTransfer.getData('text/plain');
+      const draggedSurgery = scheduleStore.scheduledSurgeries.find(s => s.id === draggedSurgeryId) ||
+                            scheduleStore.pendingSurgeries.find(s => s.id === draggedSurgeryId);
+
+      if (draggedSurgery) {
+        // Calculate SDST for this position
+        const { sdsTime, conflicts } = calculateSDSTAndConflicts(draggedSurgery, orId, proposedStartTime);
+
+        // Update ghost with real-time feedback
+        dragGhost.value.sdstStyle = {
+          width: `${sdsTime * pixelsPerMinute.value}px`,
+          backgroundColor: sdsTime <= 15 ? 'var(--color-sdst-low, rgba(40, 167, 69, 0.4))' :
+                          sdsTime <= 30 ? 'var(--color-sdst-medium, rgba(255, 193, 7, 0.4))' :
+                          'var(--color-sdst-high, rgba(220, 53, 69, 0.4))'
+        };
+
+        // Update text with conflict information
+        const conflictText = conflicts.length > 0 ? ` (${conflicts.length} conflicts)` : '';
+        dragGhost.value.text = `${draggedSurgery.patientName} - ${draggedSurgery.type} | SDST: ${sdsTime}min${conflictText}`;
+      }
     }
   }
 };
@@ -534,47 +624,44 @@ const onDrop = (event, targetORId) => {
   const minutes = newStartTime.getMinutes();
   newStartTime.setMinutes(Math.round(minutes / 15) * 15, 0, 0);
 
-  // Call store action to reschedule
-  scheduleStore.rescheduleSurgery(surgeryId, targetORId, newStartTime);
-
-  draggedSurgeryId.value = null;
-  dragGhost.value.visible = false;
-};
-
-// Get a human-readable explanation of the SDST
-const getSDSTExplanation = (surgery) => {
-  if (!surgery || !surgery.sdsTime) return '';
+  // Get the surgery being dropped
+  const surgery = scheduleStore.scheduledSurgeries.find(s => s.id === surgeryId) ||
+                 scheduleStore.pendingSurgeries.find(s => s.id === surgeryId);
+
+  if (surgery) {
+    // Perform final conflict check before dropping
+    const { sdsTime, conflicts } = calculateSDSTAndConflicts(surgery, targetORId, newStartTime);
+
+    // Show conflicts to user if any exist
+    if (conflicts.length > 0) {
+      const confirmDrop = confirm(
+        `Warning: This placement has ${conflicts.length} conflict(s):\n\n${conflicts.join('\n')}\n\nDo you want to proceed anyway?`
+      );
+
+      if (!confirmDrop) {
+        draggedSurgeryId.value = null;
+        dragGhost.value.visible = false;
+        return;
+      }
+    }
 
-  const fromType = surgery.precedingType || 'Initial';
-  const toType = surgery.type;
+    console.log(`Dropping surgery ${surgeryId} in OR ${targetORId} at ${newStartTime.toISOString()} with SDST: ${sdsTime}min`);
 
-  let explanation = '';
+    // Check if this is a pending surgery (schedule it) or existing surgery (reschedule it)
+    const isPendingSurgery = scheduleStore.pendingSurgeries.some(p => p.id === surgeryId);
 
-  if (surgery.sdsTime <= 15) {
-    explanation = `Quick transition from ${fromType} to ${toType} surgery.`;
-  } else if (surgery.sdsTime <= 30) {
-    explanation = `Standard setup time required when transitioning from ${fromType} to ${toType} surgery.`;
-  } else {
-    explanation = `Extended setup time required when transitioning from ${fromType} to ${toType} surgery.`;
+    if (isPendingSurgery) {
+      scheduleStore.addSurgeryFromPending(surgeryId, targetORId, newStartTime);
+    } else {
+      scheduleStore.rescheduleSurgery(surgeryId, targetORId, newStartTime);
+    }
   }
 
-  return explanation;
+  draggedSurgeryId.value = null;
+  dragGhost.value.visible = false;
 };
 
-// Accessibility
-const getSurgeryAccessibleLabel = (surgery) => {
-  let label = `Surgery: ${surgery.fullType} for ${surgery.patientName}, scheduled in OR ${surgery.orName} from ${formatTime(surgery.startTime)} to ${formatTime(surgery.endTime)}. Estimated duration: ${surgery.estimatedDuration} minutes.`;
 
-  if (surgery.sdsTime > 0) {
-    label += ` Requires ${surgery.sdsTime} minutes setup time due to preceding ${surgery.precedingType || 'Initial'} surgery.`;
-  }
-
-  if (surgery.conflicts && surgery.conflicts.length > 0) {
-    label += ` Alerts: ${surgery.conflicts.join(', ')}.`;
-  }
-
-  return label;
-};
 
 // Update current time indicator periodically
 let currentTimeInterval = null;
@@ -1139,12 +1226,41 @@ onUnmounted(() => {
   white-space: nowrap;
 }
 
-/* Responsive Adjustments */
+/* Enhanced Mobile Responsive Design */
+
+/* Tablet adjustments (768px - 1024px) */
+@media (max-width: 1024px) {
+  .gantt-header {
+    flex-direction: column;
+    align-items: flex-start;
+    gap: var(--spacing-sm);
+    padding: var(--spacing-sm);
+  }
+
+  .view-controls {
+    width: 100%;
+    flex-wrap: wrap;
+    gap: var(--spacing-sm);
+  }
+
+  .view-controls select,
+  .view-controls button {
+    min-height: var(--touch-target-min);
+    padding: var(--spacing-sm) var(--spacing-md);
+  }
+}
+
+/* Mobile adjustments (up to 768px) */
 @media (max-width: 768px) {
+  .gantt-chart {
+    font-size: var(--font-size-sm);
+  }
+
   .gantt-header {
     flex-direction: column;
     align-items: flex-start;
     gap: var(--spacing-sm);
+    padding: var(--spacing-sm);
   }
 
   .view-controls {
@@ -1153,8 +1269,18 @@ onUnmounted(() => {
     gap: var(--spacing-sm);
   }
 
+  .view-controls select,
+  .view-controls button {
+    width: 100%;
+    min-height: var(--touch-target-comfortable);
+    padding: var(--spacing-sm) var(--spacing-md);
+    font-size: var(--font-size-base);
+  }
+
   .or-label, .time-marker {
-    width: 80px; /* Smaller width on mobile */
+    width: 60px; /* Smaller width on mobile */
+    font-size: var(--font-size-xs);
+    padding: var(--spacing-xs);
   }
 
   .or-timeline {
@@ -1163,10 +1289,32 @@ onUnmounted(() => {
       var(--color-border-soft) 0px,
       var(--color-border-soft) 1px,
       transparent 1px,
-      transparent 80px /* Smaller width per hour on mobile */
+      transparent 60px /* Smaller width per hour on mobile */
     );
   }
 
+  .surgery-block {
+    min-height: 40px; /* Larger touch targets */
+    font-size: var(--font-size-xs);
+    padding: var(--spacing-xs);
+  }
+
+  .surgery-block .surgery-info {
+    flex-direction: column;
+    align-items: flex-start;
+    gap: 2px;
+  }
+
+  .surgery-block .surgery-title {
+    font-size: var(--font-size-xs);
+    font-weight: var(--font-weight-bold);
+  }
+
+  .surgery-block .surgery-details {
+    font-size: 10px;
+    opacity: 0.8;
+  }
+
   .sdst-legend {
     padding: var(--spacing-xs);
     flex-direction: column;
@@ -1175,15 +1323,128 @@ onUnmounted(() => {
 
   .legend-items {
     margin-top: var(--spacing-xs);
+    flex-wrap: wrap;
+    gap: var(--spacing-xs);
   }
 
   .legend-item {
-    margin-right: var(--spacing-sm);
+    margin-right: 0;
+    margin-bottom: var(--spacing-xs);
+    font-size: var(--font-size-xs);
   }
 
   .sdst-label {
     display: none; /* Hide SDST labels on mobile */
   }
+
+  /* Mobile-specific gantt container */
+  .gantt-container {
+    overflow-x: auto;
+    -webkit-overflow-scrolling: touch;
+  }
+
+  /* Simplified mobile view */
+  .mobile-simplified .surgery-block .surgery-details {
+    display: none;
+  }
+
+  .mobile-simplified .sdst-indicator {
+    display: none;
+  }
+}
+
+/* Small mobile adjustments (up to 480px) */
+@media (max-width: 480px) {
+  .gantt-header {
+    padding: var(--spacing-xs);
+  }
+
+  .or-label, .time-marker {
+    width: 50px;
+    font-size: 10px;
+  }
+
+  .or-timeline {
+    background-image: repeating-linear-gradient(
+      to right,
+      var(--color-border-soft) 0px,
+      var(--color-border-soft) 1px,
+      transparent 1px,
+      transparent 50px
+    );
+  }
+
+  .surgery-block {
+    min-height: 36px;
+    font-size: 10px;
+  }
+
+  .surgery-block .surgery-title {
+    font-size: 10px;
+  }
+
+  .legend-item {
+    font-size: 10px;
+  }
+
+  .legend-color {
+    width: 16px;
+    height: 8px;
+  }
+
+  .legend-icon {
+    width: 14px;
+    height: 14px;
+    font-size: 8px;
+  }
+}
+
+/* Touch-specific enhancements */
+@media (hover: none) and (pointer: coarse) {
+  .surgery-block {
+    min-height: var(--touch-target-comfortable);
+    cursor: default;
+  }
+
+  .surgery-block:hover {
+    transform: none; /* Disable hover transforms on touch devices */
+  }
+
+  .surgery-block:active {
+    transform: scale(0.98);
+    transition: transform 0.1s ease;
+  }
+
+  .view-controls button:active {
+    transform: scale(0.95);
+  }
+}
+
+/* Landscape orientation for mobile */
+@media (max-height: 500px) and (orientation: landscape) {
+  .gantt-header {
+    flex-direction: row;
+    justify-content: space-between;
+    align-items: center;
+    padding: var(--spacing-xs) var(--spacing-sm);
+  }
+
+  .view-controls {
+    flex-direction: row;
+    width: auto;
+  }
+
+  .view-controls select,
+  .view-controls button {
+    width: auto;
+    min-height: 36px;
+    padding: var(--spacing-xs) var(--spacing-sm);
+    font-size: var(--font-size-sm);
+  }
+
+  .surgery-block {
+    min-height: 32px;
+  }
 }
 
 /* Accessibility Enhancements */
diff --git a/src/components/MasterScheduleScreen.vue b/src/components/MasterScheduleScreen.vue
index f0d02f4..6f61f9a 100644
--- a/src/components/MasterScheduleScreen.vue
+++ b/src/components/MasterScheduleScreen.vue
@@ -1,96 +1,325 @@
 <template>
   <div class="master-schedule-container">
-    <h1>Master Surgery Schedule</h1>
-    <div v-if="isLoading" class="loading-message">
-      Loading schedule data...
+    <div class="schedule-header">
+      <div class="header-content">
+        <h1>Master Surgery Schedule</h1>
+        <p class="schedule-description">
+          Interactive Gantt chart showing all scheduled surgeries with SDST (Sequence-Dependent Setup Times) visualization
+        </p>
+      </div>
+      <div class="header-actions">
+        <button @click="addNewSurgery" class="btn btn-primary">
+          <span class="icon">Γ₧ò</span> Add New Surgery
+        </button>
+        <button @click="goToScheduling" class="btn btn-secondary">
+          <span class="icon">≡ƒô¥</span> Scheduling Interface
+        </button>
+      </div>
+    </div>
+
+    <div v-if="scheduleStore.isLoading" class="loading-message">
+      <div class="loading-spinner"></div>
+      <p>Loading schedule data...</p>
     </div>
-    <div v-else>
-      <!-- Placeholder for calendar view -->
-      <div class="calendar-controls">
-        <button @click="showDayView">Day</button>
-        <button @click="showWeekView">Week</button>
-        <button @click="showMonthView">Month</button>
-        <button class="button-primary" @click="addNewSurgery">Add New Surgery</button>
+
+    <div v-else class="gantt-section">
+      <!-- Gantt Chart Integration -->
+      <div class="gantt-wrapper">
+        <GanttChart />
       </div>
-      <div class="calendar-view">
-        <!-- Calendar library component will be rendered here -->
-        <div class="calendar-day">
-        Day 3
-          <p v-for="surgery in scheduledSurgeries.filter(s => s.day === 3)" :key="surgery.id">{{ surgery.time }} - {{ surgery.patient }} ({{ surgery.procedure }})</p>
+
+      <!-- Quick Stats Panel -->
+      <div class="quick-stats">
+        <div class="stat-card">
+          <div class="stat-value">{{ totalSurgeriesToday }}</div>
+          <div class="stat-label">Surgeries Today</div>
+        </div>
+        <div class="stat-card">
+          <div class="stat-value">{{ averageSDSTToday }}m</div>
+          <div class="stat-label">Avg. SDST</div>
+        </div>
+        <div class="stat-card">
+          <div class="stat-value">{{ orUtilizationToday }}%</div>
+          <div class="stat-label">OR Utilization</div>
+        </div>
+        <div class="stat-card">
+          <div class="stat-value">{{ conflictsToday }}</div>
+          <div class="stat-label">Conflicts</div>
         </div>
       </div>
     </div>
   </div>
-    </div>
 </template>
 
 <script setup>
-// Script logic will be added here later
-import { ref, onMounted } from 'vue';
+import { ref, computed, onMounted } from 'vue';
+import { useRouter } from 'vue-router';
+import { useScheduleStore } from '@/stores/scheduleStore';
+import { storeToRefs } from 'pinia';
+import GanttChart from './GanttChart.vue';
 
-// TODO: Integrate a calendar library here for robust calendar functionality.
+const router = useRouter();
+const scheduleStore = useScheduleStore();
 
+// Use storeToRefs to get reactive state from the store
+const { visibleScheduledSurgeries, isLoading } = storeToRefs(scheduleStore);
 
-// Loading state
-const isLoading = ref(true);
+// Computed properties for quick stats
+const totalSurgeriesToday = computed(() => {
+  const today = new Date().toDateString();
+  return visibleScheduledSurgeries.value.filter(surgery => {
+    const surgeryDate = new Date(surgery.startTime).toDateString();
+    return surgeryDate === today;
+  }).length;
+});
 
-// Simulated data for scheduled surgeries
-const scheduledSurgeries = ref([
-    { id: 1, day: 1, time: '08:00', patient: 'Patient X', procedure: 'Appendectomy' },
-    { id: 2, day: 1, time: '10:00', patient: 'Patient Y', procedure: 'Hernia Repair' },
-    { id: 3, day: 2, time: '09:00', patient: 'Patient Z', procedure: 'Cholecystectomy' },
-    { id: 4, day: 3, time: '11:00', patient: 'Patient A', procedure: 'Knee Arthroscopy' },
-]);
+const averageSDSTToday = computed(() => {
+  const todaySurgeries = visibleScheduledSurgeries.value.filter(surgery => {
+    const today = new Date().toDateString();
+    const surgeryDate = new Date(surgery.startTime).toDateString();
+    return surgeryDate === today && surgery.sdsTime;
+  });
 
-// Placeholder methods for switching calendar views
-const showDayView = () => {
-  console.log('Switching to Day View');
-  // TODO: Call calendar library API to switch to day view
-};
+  if (todaySurgeries.length === 0) return 0;
 
-const showWeekView = () => {
-  console.log('Switching to Week View');
-  // TODO: Call calendar library API to switch to week view
-};
+  const totalSDST = todaySurgeries.reduce((sum, surgery) => sum + (surgery.sdsTime || 0), 0);
+  return Math.round(totalSDST / todaySurgeries.length);
+});
 
-const showMonthView = () => {
-  console.log('Switching to Month View');
-  // TODO: Call calendar library API to switch to month view
-};
-// Placeholder method for adding a new surgery
+const orUtilizationToday = computed(() => {
+  // Calculate OR utilization based on scheduled time vs available time
+  // This is a simplified calculation
+  const totalScheduledMinutes = visibleScheduledSurgeries.value
+    .filter(surgery => {
+      const today = new Date().toDateString();
+      const surgeryDate = new Date(surgery.startTime).toDateString();
+      return surgeryDate === today;
+    })
+    .reduce((sum, surgery) => sum + (surgery.duration || 0), 0);
+
+  // Assuming 8 ORs with 12 hours each = 5760 minutes total capacity per day
+  const totalCapacity = 8 * 12 * 60;
+  return Math.round((totalScheduledMinutes / totalCapacity) * 100);
+});
+
+const conflictsToday = computed(() => {
+  const today = new Date().toDateString();
+  return visibleScheduledSurgeries.value.filter(surgery => {
+    const surgeryDate = new Date(surgery.startTime).toDateString();
+    return surgeryDate === today && surgery.conflicts && surgery.conflicts.length > 0;
+  }).length;
+});
+
+// Navigation methods
 const addNewSurgery = () => {
-  console.log("Add New Surgery button clicked");
+  console.log("Navigating to add new surgery");
+  router.push({ name: 'Scheduling' });
 };
 
-// Simulate fetching data on component mount
-const fetchScheduleData = async () => {
-  // Simulate network delay
-  await new Promise(resolve => setTimeout(resolve, 1000));
-  isLoading.value = false; // Set loading to false after data is "fetched"
+const goToScheduling = () => {
+  console.log("Navigating to scheduling interface");
+  router.push({ name: 'Scheduling' });
 };
-onMounted(fetchScheduleData);
+
+// Load initial data when component mounts
+onMounted(() => {
+  console.log('MasterScheduleScreen mounted. Loading schedule data...');
+  // The scheduleStore should already have data loaded, but we can ensure it's loaded
+  if (!scheduleStore.isDataLoaded) {
+    scheduleStore.loadInitialData();
+  }
+});
 </script>
 
 <style scoped>
 .master-schedule-container {
-  padding: 20px;
+  padding: var(--spacing-lg);
+  background-color: var(--color-background);
+  min-height: 100vh;
 }
 
-.calendar-view {
-  display: grid;
-  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); /* Example responsive grid */
-  gap: 10px; /* Space between days */
+.schedule-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: flex-start;
+  margin-bottom: var(--spacing-lg);
+  padding: var(--spacing-lg);
+  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
+  color: white;
+  border-radius: var(--border-radius-lg);
+  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
+}
+
+.header-content h1 {
+  margin: 0 0 var(--spacing-sm) 0;
+  font-size: var(--font-size-xxl);
+  font-weight: var(--font-weight-bold);
+}
+
+.schedule-description {
+  margin: 0;
+  font-size: var(--font-size-base);
+  opacity: 0.9;
+  max-width: 600px;
+}
+
+.header-actions {
+  display: flex;
+  gap: var(--spacing-sm);
+  flex-shrink: 0;
+}
+
+.header-actions .btn {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-xs);
+  padding: var(--spacing-sm) var(--spacing-md);
+  border-radius: var(--border-radius-md);
+  font-weight: var(--font-weight-medium);
+  transition: all 0.2s ease;
+}
+
+.header-actions .btn-primary {
+  background-color: white;
+  color: var(--color-primary);
+  border: 2px solid white;
+}
+
+.header-actions .btn-primary:hover {
+  background-color: var(--color-primary-light);
+  color: white;
+  transform: translateY(-1px);
+}
+
+.header-actions .btn-secondary {
+  background-color: transparent;
+  color: white;
+  border: 2px solid white;
 }
 
-.calendar-day {
-  border: 1px solid var(--color-mid-light-gray); /* Subtle border */
-  padding: 10px;
-  min-height: 150px; /* Minimum height for a day cell */
+.header-actions .btn-secondary:hover {
+  background-color: white;
+  color: var(--color-primary);
+  transform: translateY(-1px);
 }
 
-/* Basic loading message styling */
 .loading-message {
+  display: flex;
+  flex-direction: column;
+  align-items: center;
+  justify-content: center;
+  padding: var(--spacing-xl);
+  text-align: center;
+  color: var(--color-text-secondary);
+}
+
+.loading-spinner {
+  width: 40px;
+  height: 40px;
+  border: 4px solid var(--color-border);
+  border-top: 4px solid var(--color-primary);
+  border-radius: 50%;
+  animation: spin 1s linear infinite;
+  margin-bottom: var(--spacing-md);
+}
+
+@keyframes spin {
+  0% { transform: rotate(0deg); }
+  100% { transform: rotate(360deg); }
+}
+
+.gantt-section {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-lg);
+}
+
+.gantt-wrapper {
+  background-color: var(--color-surface);
+  border-radius: var(--border-radius-lg);
+  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
+  overflow: hidden;
+  min-height: 600px;
+}
+
+.quick-stats {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
+  gap: var(--spacing-md);
+  margin-top: var(--spacing-md);
+}
+
+.stat-card {
+  background-color: var(--color-surface);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
+  text-align: center;
+  transition: transform 0.2s ease, box-shadow 0.2s ease;
+}
+
+.stat-card:hover {
+  transform: translateY(-2px);
+  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
+}
+
+.stat-value {
+  font-size: var(--font-size-xxl);
+  font-weight: var(--font-weight-bold);
+  color: var(--color-primary);
+  margin-bottom: var(--spacing-xs);
+}
+
+.stat-label {
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+  font-weight: var(--font-weight-medium);
+  text-transform: uppercase;
+  letter-spacing: 0.5px;
+}
+
+/* Mobile responsiveness */
+@media (max-width: 768px) {
+  .master-schedule-container {
+    padding: var(--spacing-md);
+  }
+
+  .schedule-header {
+    flex-direction: column;
+    gap: var(--spacing-md);
     text-align: center;
-    color: var(--color-dark-gray);
+  }
+
+  .header-actions {
+    justify-content: center;
+    flex-wrap: wrap;
+  }
+
+  .quick-stats {
+    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
+    gap: var(--spacing-sm);
+  }
+
+  .stat-card {
+    padding: var(--spacing-md);
+  }
+
+  .stat-value {
+    font-size: var(--font-size-xl);
+  }
+}
+
+@media (max-width: 480px) {
+  .header-content h1 {
+    font-size: var(--font-size-xl);
+  }
+
+  .schedule-description {
+    font-size: var(--font-size-sm);
+  }
+
+  .quick-stats {
+    grid-template-columns: 1fr 1fr;
+  }
 }
 </style>
\ No newline at end of file
diff --git a/src/components/MobileTestComponent.vue b/src/components/MobileTestComponent.vue
new file mode 100644
index 0000000..e4c14b7
--- /dev/null
+++ b/src/components/MobileTestComponent.vue
@@ -0,0 +1,248 @@
+<template>
+  <div class="mobile-test-component">
+    <h1>Mobile Responsiveness Test</h1>
+    
+    <!-- Screen Size Indicator -->
+    <div class="screen-info">
+      <div class="info-card">
+        <h3>Current Screen Info</h3>
+        <p><strong>Width:</strong> {{ screenWidth }}px</p>
+        <p><strong>Height:</strong> {{ screenHeight }}px</p>
+        <p><strong>Device Type:</strong> {{ deviceType }}</p>
+        <p><strong>Orientation:</strong> {{ orientation }}</p>
+        <p><strong>Touch Device:</strong> {{ isTouchDevice ? 'Yes' : 'No' }}</p>
+      </div>
+    </div>
+
+    <!-- Responsive Grid Test -->
+    <div class="test-section">
+      <h2>Responsive Grid Test</h2>
+      <div class="responsive-grid">
+        <div class="grid-item">Item 1</div>
+        <div class="grid-item">Item 2</div>
+        <div class="grid-item">Item 3</div>
+        <div class="grid-item">Item 4</div>
+      </div>
+    </div>
+
+    <!-- Touch Target Test -->
+    <div class="test-section">
+      <h2>Touch Target Test</h2>
+      <div class="touch-test">
+        <button class="btn-touch">Touch-Friendly Button</button>
+        <button class="btn-regular">Regular Button</button>
+        <input type="text" placeholder="Touch-friendly input" class="form-control-mobile">
+      </div>
+    </div>
+
+    <!-- Mobile Navigation Test -->
+    <div class="test-section">
+      <h2>Mobile Navigation Test</h2>
+      <div class="mobile-nav-test">
+        <div class="mobile-only">Mobile Only Content</div>
+        <div class="tablet-only">Tablet Only Content</div>
+        <div class="desktop-only">Desktop Only Content</div>
+      </div>
+    </div>
+
+    <!-- Responsive Typography Test -->
+    <div class="test-section">
+      <h2>Typography Test</h2>
+      <div class="typography-test">
+        <h1>Heading 1</h1>
+        <h2>Heading 2</h2>
+        <h3>Heading 3</h3>
+        <p>This is a paragraph of text that should be readable on all devices.</p>
+        <small>Small text for mobile devices</small>
+      </div>
+    </div>
+
+    <!-- Spacing Test -->
+    <div class="test-section">
+      <h2>Spacing Test</h2>
+      <div class="spacing-test">
+        <div class="mobile-p-sm">Mobile Small Padding</div>
+        <div class="mobile-p-md">Mobile Medium Padding</div>
+        <div class="mobile-p-lg">Mobile Large Padding</div>
+      </div>
+    </div>
+  </div>
+</template>
+
+<script setup>
+import { ref, computed, onMounted, onUnmounted } from 'vue';
+
+// Reactive screen dimensions
+const screenWidth = ref(window.innerWidth);
+const screenHeight = ref(window.innerHeight);
+
+// Computed properties for device detection
+const deviceType = computed(() => {
+  if (screenWidth.value < 480) return 'Small Mobile';
+  if (screenWidth.value < 768) return 'Mobile';
+  if (screenWidth.value < 1024) return 'Tablet';
+  if (screenWidth.value < 1200) return 'Small Desktop';
+  return 'Desktop';
+});
+
+const orientation = computed(() => {
+  return screenWidth.value > screenHeight.value ? 'Landscape' : 'Portrait';
+});
+
+const isTouchDevice = computed(() => {
+  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
+});
+
+// Handle window resize
+const handleResize = () => {
+  screenWidth.value = window.innerWidth;
+  screenHeight.value = window.innerHeight;
+};
+
+// Lifecycle hooks
+onMounted(() => {
+  window.addEventListener('resize', handleResize);
+});
+
+onUnmounted(() => {
+  window.removeEventListener('resize', handleResize);
+});
+</script>
+
+<style scoped>
+.mobile-test-component {
+  padding: var(--spacing-md);
+  max-width: 1200px;
+  margin: 0 auto;
+}
+
+.screen-info {
+  margin-bottom: var(--spacing-lg);
+}
+
+.info-card {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-md);
+  border-left: 4px solid var(--color-primary);
+}
+
+.test-section {
+  margin-bottom: var(--spacing-xl);
+  padding: var(--spacing-md);
+  border: 1px solid var(--color-border);
+  border-radius: var(--border-radius-md);
+}
+
+.test-section h2 {
+  margin-top: 0;
+  color: var(--color-primary);
+}
+
+/* Grid test styles */
+.grid-item {
+  background-color: var(--color-primary);
+  color: white;
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  text-align: center;
+  font-weight: var(--font-weight-medium);
+}
+
+/* Touch test styles */
+.touch-test {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-md);
+  align-items: flex-start;
+}
+
+.btn-regular {
+  padding: var(--spacing-xs) var(--spacing-sm);
+  background-color: var(--color-background-mute);
+  border: 1px solid var(--color-border);
+  border-radius: var(--border-radius-sm);
+  cursor: pointer;
+}
+
+/* Mobile nav test styles */
+.mobile-nav-test {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-sm);
+}
+
+.mobile-nav-test > div {
+  padding: var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  text-align: center;
+  font-weight: var(--font-weight-medium);
+}
+
+.mobile-only {
+  background-color: var(--color-success);
+  color: white;
+}
+
+.tablet-only {
+  background-color: var(--color-warning);
+  color: white;
+}
+
+.desktop-only {
+  background-color: var(--color-primary);
+  color: white;
+}
+
+/* Typography test styles */
+.typography-test {
+  line-height: 1.6;
+}
+
+/* Spacing test styles */
+.spacing-test {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-sm);
+}
+
+.spacing-test > div {
+  background-color: var(--color-background-mute);
+  border: 1px solid var(--color-border);
+  border-radius: var(--border-radius-sm);
+}
+
+/* Mobile responsive adjustments */
+@media (max-width: 768px) {
+  .mobile-test-component {
+    padding: var(--spacing-sm);
+  }
+  
+  .test-section {
+    padding: var(--spacing-sm);
+  }
+  
+  .touch-test {
+    width: 100%;
+  }
+  
+  .touch-test button,
+  .touch-test input {
+    width: 100%;
+  }
+}
+
+@media (max-width: 480px) {
+  .mobile-test-component {
+    padding: var(--spacing-xs);
+  }
+  
+  .info-card {
+    padding: var(--spacing-sm);
+  }
+  
+  .test-section {
+    padding: var(--spacing-xs);
+  }
+}
+</style>
diff --git a/src/components/NotificationsScreen.vue b/src/components/NotificationsScreen.vue
index 3a30027..8ae65a5 100644
--- a/src/components/NotificationsScreen.vue
+++ b/src/components/NotificationsScreen.vue
@@ -1,25 +1,788 @@
 <template>
-  <div class="section-container">
-    <h1>Notifications</h1>
-    <!-- Placeholder for Notifications List -->
-    <div>
-      <ul>
-        <li>Notification 1: Important update</li>
-        <li>Notification 2: Action required</li>
-        <li>Notification 3: Surgery scheduled</li>
-      </ul>
-      <!-- Actual notifications would be fetched and displayed here -->
+  <div class="notifications-container">
+    <!-- Enhanced Header -->
+    <div class="notifications-header">
+      <div class="header-content">
+        <h1>Notifications & Alerts</h1>
+        <p class="header-subtitle">Stay informed about critical updates and system alerts</p>
+      </div>
+      <div class="header-actions">
+        <button @click="markAllAsRead" class="btn btn-secondary" :disabled="unreadCount === 0">
+          <span class="icon">Γ£ô</span> Mark All Read ({{ unreadCount }})
+        </button>
+        <button @click="clearAll" class="btn btn-outline">
+          <span class="icon">≡ƒùæ∩╕Å</span> Clear All
+        </button>
+        <button @click="refreshNotifications" class="btn btn-primary">
+          <span class="icon">≡ƒöä</span> Refresh
+        </button>
+      </div>
     </div>
 
+    <!-- Filter Tabs -->
+    <div class="notification-filters">
+      <button
+        v-for="filter in filters"
+        :key="filter.key"
+        @click="activeFilter = filter.key"
+        :class="['filter-tab', { active: activeFilter === filter.key }]"
+      >
+        <span class="filter-icon">{{ filter.icon }}</span>
+        <span class="filter-label">{{ filter.label }}</span>
+        <span v-if="filter.count > 0" class="filter-count">{{ filter.count }}</span>
+      </button>
+    </div>
+
+    <!-- Loading State -->
+    <div v-if="isLoading" class="loading-message">
+      <div class="loading-spinner"></div>
+      <p>Loading notifications...</p>
+    </div>
+
+    <!-- Notifications List -->
+    <div v-else class="notifications-list">
+      <div v-if="filteredNotifications.length === 0" class="empty-state">
+        <div class="empty-icon">≡ƒô¡</div>
+        <h3>No notifications</h3>
+        <p>{{ getEmptyStateMessage() }}</p>
+      </div>
+
+      <div v-else class="notification-items">
+        <div
+          v-for="notification in filteredNotifications"
+          :key="notification.id"
+          :class="['notification-item', notification.type, { unread: !notification.read }]"
+          @click="markAsRead(notification)"
+        >
+          <div class="notification-icon">
+            <span>{{ getNotificationIcon(notification.type) }}</span>
+          </div>
+          <div class="notification-content">
+            <div class="notification-header">
+              <h4 class="notification-title">{{ notification.title }}</h4>
+              <span class="notification-time">{{ formatTime(notification.timestamp) }}</span>
+            </div>
+            <p class="notification-message">{{ notification.message }}</p>
+            <div v-if="notification.actions" class="notification-actions">
+              <button
+                v-for="action in notification.actions"
+                :key="action.label"
+                @click.stop="handleAction(action, notification)"
+                :class="['action-btn', action.type]"
+              >
+                {{ action.label }}
+              </button>
+            </div>
+          </div>
+          <div class="notification-controls">
+            <button @click.stop="dismissNotification(notification)" class="dismiss-btn">
+              <span>├ù</span>
+            </button>
+          </div>
+        </div>
+      </div>
+    </div>
   </div>
 </template>
 
 <script setup>
-// Component logic will go here later
+import { ref, computed, onMounted } from 'vue';
+import { useRouter } from 'vue-router';
+
+const router = useRouter();
+
+// Reactive state
+const isLoading = ref(false);
+const activeFilter = ref('all');
+const notifications = ref([]);
+
+// Sample notification data - in real app, this would come from an API/store
+const sampleNotifications = [
+  {
+    id: 1,
+    type: 'critical',
+    title: 'Emergency Surgery Scheduled',
+    message: 'Emergency appendectomy scheduled for OR 3 at 14:30. Immediate attention required.',
+    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
+    read: false,
+    actions: [
+      { label: 'View Details', type: 'primary', action: 'view-surgery' },
+      { label: 'Acknowledge', type: 'secondary', action: 'acknowledge' }
+    ]
+  },
+  {
+    id: 2,
+    type: 'warning',
+    title: 'SDST Conflict Detected',
+    message: 'Potential setup time conflict between surgeries in OR 2. Review scheduling.',
+    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
+    read: false,
+    actions: [
+      { label: 'Resolve Conflict', type: 'primary', action: 'resolve-conflict' },
+      { label: 'Ignore', type: 'secondary', action: 'ignore' }
+    ]
+  },
+  {
+    id: 3,
+    type: 'info',
+    title: 'Daily Schedule Optimized',
+    message: 'Schedule optimization completed. 15% improvement in OR utilization achieved.',
+    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
+    read: true,
+    actions: [
+      { label: 'View Report', type: 'primary', action: 'view-report' }
+    ]
+  },
+  {
+    id: 4,
+    type: 'success',
+    title: 'Surgery Completed Successfully',
+    message: 'Knee arthroscopy in OR 1 completed successfully. Patient transferred to recovery.',
+    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
+    read: true
+  },
+  {
+    id: 5,
+    type: 'warning',
+    title: 'Equipment Maintenance Due',
+    message: 'Anesthesia machine in OR 4 requires scheduled maintenance within 48 hours.',
+    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
+    read: false,
+    actions: [
+      { label: 'Schedule Maintenance', type: 'primary', action: 'schedule-maintenance' },
+      { label: 'Postpone', type: 'secondary', action: 'postpone' }
+    ]
+  },
+  {
+    id: 6,
+    type: 'info',
+    title: 'New Staff Member Added',
+    message: 'Dr. Sarah Johnson has been added to the surgical team for cardiovascular procedures.',
+    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
+    read: true
+  }
+];
+
+// Filter definitions
+const filters = computed(() => [
+  {
+    key: 'all',
+    label: 'All',
+    icon: '≡ƒôï',
+    count: notifications.value.length
+  },
+  {
+    key: 'unread',
+    label: 'Unread',
+    icon: '≡ƒö┤',
+    count: notifications.value.filter(n => !n.read).length
+  },
+  {
+    key: 'critical',
+    label: 'Critical',
+    icon: '≡ƒÜ¿',
+    count: notifications.value.filter(n => n.type === 'critical').length
+  },
+  {
+    key: 'warning',
+    label: 'Warnings',
+    icon: 'ΓÜá∩╕Å',
+    count: notifications.value.filter(n => n.type === 'warning').length
+  },
+  {
+    key: 'info',
+    label: 'Info',
+    icon: 'Γä╣∩╕Å',
+    count: notifications.value.filter(n => n.type === 'info').length
+  },
+  {
+    key: 'success',
+    label: 'Success',
+    icon: 'Γ£à',
+    count: notifications.value.filter(n => n.type === 'success').length
+  }
+]);
+
+// Computed properties
+const filteredNotifications = computed(() => {
+  let filtered = notifications.value;
+
+  switch (activeFilter.value) {
+    case 'unread':
+      filtered = filtered.filter(n => !n.read);
+      break;
+    case 'critical':
+    case 'warning':
+    case 'info':
+    case 'success':
+      filtered = filtered.filter(n => n.type === activeFilter.value);
+      break;
+    default:
+      // 'all' - no filtering
+      break;
+  }
+
+  // Sort by timestamp (newest first)
+  return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
+});
+
+const unreadCount = computed(() => {
+  return notifications.value.filter(n => !n.read).length;
+});
+
+// Methods
+const getNotificationIcon = (type) => {
+  const icons = {
+    critical: '≡ƒÜ¿',
+    warning: 'ΓÜá∩╕Å',
+    info: 'Γä╣∩╕Å',
+    success: 'Γ£à'
+  };
+  return icons[type] || 'Γä╣∩╕Å';
+};
+
+const formatTime = (timestamp) => {
+  const now = new Date();
+  const diff = now - new Date(timestamp);
+  const minutes = Math.floor(diff / (1000 * 60));
+  const hours = Math.floor(diff / (1000 * 60 * 60));
+  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
+
+  if (minutes < 60) {
+    return `${minutes}m ago`;
+  } else if (hours < 24) {
+    return `${hours}h ago`;
+  } else {
+    return `${days}d ago`;
+  }
+};
+
+const getEmptyStateMessage = () => {
+  switch (activeFilter.value) {
+    case 'unread':
+      return 'All notifications have been read.';
+    case 'critical':
+      return 'No critical alerts at this time.';
+    case 'warning':
+      return 'No warnings to display.';
+    case 'info':
+      return 'No informational notifications.';
+    case 'success':
+      return 'No success notifications.';
+    default:
+      return 'No notifications available.';
+  }
+};
+
+const markAsRead = (notification) => {
+  if (!notification.read) {
+    notification.read = true;
+    console.log('Marked notification as read:', notification.title);
+  }
+};
+
+const markAllAsRead = () => {
+  notifications.value.forEach(n => {
+    n.read = true;
+  });
+  console.log('Marked all notifications as read');
+};
+
+const dismissNotification = (notification) => {
+  const index = notifications.value.findIndex(n => n.id === notification.id);
+  if (index > -1) {
+    notifications.value.splice(index, 1);
+    console.log('Dismissed notification:', notification.title);
+  }
+};
+
+const clearAll = () => {
+  if (confirm('Are you sure you want to clear all notifications?')) {
+    notifications.value = [];
+    console.log('Cleared all notifications');
+  }
+};
+
+const refreshNotifications = () => {
+  isLoading.value = true;
+  console.log('Refreshing notifications...');
+
+  // Simulate API call
+  setTimeout(() => {
+    // In real app, this would fetch from API
+    notifications.value = [...sampleNotifications];
+    isLoading.value = false;
+    console.log('Notifications refreshed');
+  }, 1000);
+};
+
+const handleAction = (action, notification) => {
+  console.log('Handling action:', action.action, 'for notification:', notification.title);
+
+  switch (action.action) {
+    case 'view-surgery':
+      router.push({ name: 'Scheduling' });
+      break;
+    case 'resolve-conflict':
+      router.push({ name: 'MasterSchedule' });
+      break;
+    case 'view-report':
+      console.log('Navigate to reports');
+      break;
+    case 'schedule-maintenance':
+      console.log('Navigate to maintenance scheduling');
+      break;
+    case 'acknowledge':
+    case 'ignore':
+    case 'postpone':
+      markAsRead(notification);
+      break;
+    default:
+      console.log('Unknown action:', action.action);
+  }
+};
+
+// Initialize component
+onMounted(() => {
+  console.log('NotificationsScreen mounted');
+  notifications.value = [...sampleNotifications];
+});
 </script>
 
 <style scoped>
-.section-container {
-  padding: 20px;
+.notifications-container {
+  padding: var(--spacing-lg);
+  background-color: var(--color-background);
+  min-height: 100vh;
+}
+
+/* Enhanced Header */
+.notifications-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: flex-start;
+  margin-bottom: var(--spacing-xl);
+  padding: var(--spacing-xl);
+  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
+  color: white;
+  border-radius: var(--border-radius-lg);
+  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
+}
+
+.header-content h1 {
+  margin: 0 0 var(--spacing-xs) 0;
+  font-size: var(--font-size-xxl);
+  font-weight: var(--font-weight-bold);
+}
+
+.header-subtitle {
+  margin: 0;
+  font-size: var(--font-size-base);
+  opacity: 0.9;
+  font-weight: var(--font-weight-normal);
+}
+
+.header-actions {
+  display: flex;
+  gap: var(--spacing-sm);
+  flex-wrap: wrap;
+}
+
+.header-actions .btn {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-xs);
+  padding: var(--spacing-sm) var(--spacing-md);
+  border-radius: var(--border-radius-md);
+  font-weight: var(--font-weight-medium);
+  transition: all 0.2s ease;
+  border: 2px solid transparent;
+}
+
+.header-actions .btn-primary {
+  background-color: white;
+  color: var(--color-primary);
+  border-color: white;
+}
+
+.header-actions .btn-primary:hover {
+  background-color: var(--color-primary-light);
+  color: white;
+  transform: translateY(-1px);
+}
+
+.header-actions .btn-secondary {
+  background-color: transparent;
+  color: white;
+  border-color: white;
+}
+
+.header-actions .btn-secondary:hover:not(:disabled) {
+  background-color: white;
+  color: var(--color-primary);
+  transform: translateY(-1px);
+}
+
+.header-actions .btn-secondary:disabled {
+  opacity: 0.5;
+  cursor: not-allowed;
+}
+
+.header-actions .btn-outline {
+  background-color: transparent;
+  color: white;
+  border-color: rgba(255, 255, 255, 0.5);
+}
+
+.header-actions .btn-outline:hover {
+  background-color: rgba(255, 255, 255, 0.1);
+  border-color: white;
+  transform: translateY(-1px);
+}
+
+/* Filter Tabs */
+.notification-filters {
+  display: flex;
+  gap: var(--spacing-xs);
+  margin-bottom: var(--spacing-lg);
+  overflow-x: auto;
+  padding-bottom: var(--spacing-xs);
+}
+
+.filter-tab {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-xs);
+  padding: var(--spacing-sm) var(--spacing-md);
+  background: var(--color-surface);
+  border: 2px solid var(--color-border);
+  border-radius: var(--border-radius-md);
+  color: var(--color-text-secondary);
+  font-weight: var(--font-weight-medium);
+  cursor: pointer;
+  transition: all 0.2s ease;
+  white-space: nowrap;
+}
+
+.filter-tab:hover {
+  border-color: var(--color-primary);
+  color: var(--color-primary);
+  transform: translateY(-1px);
+}
+
+.filter-tab.active {
+  background: var(--color-primary);
+  border-color: var(--color-primary);
+  color: white;
+}
+
+.filter-count {
+  background: rgba(255, 255, 255, 0.2);
+  color: white;
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  font-size: var(--font-size-xs);
+  font-weight: var(--font-weight-bold);
+  min-width: 20px;
+  text-align: center;
+}
+
+.filter-tab:not(.active) .filter-count {
+  background: var(--color-primary);
+  color: white;
+}
+
+/* Loading State */
+.loading-message {
+  display: flex;
+  flex-direction: column;
+  align-items: center;
+  justify-content: center;
+  padding: var(--spacing-xl);
+  text-align: center;
+  color: var(--color-text-secondary);
+}
+
+.loading-spinner {
+  width: 40px;
+  height: 40px;
+  border: 4px solid var(--color-border);
+  border-top: 4px solid var(--color-primary);
+  border-radius: 50%;
+  animation: spin 1s linear infinite;
+  margin-bottom: var(--spacing-md);
+}
+
+@keyframes spin {
+  0% { transform: rotate(0deg); }
+  100% { transform: rotate(360deg); }
+}
+
+/* Empty State */
+.empty-state {
+  text-align: center;
+  padding: var(--spacing-xl);
+  color: var(--color-text-secondary);
+}
+
+.empty-icon {
+  font-size: 4rem;
+  margin-bottom: var(--spacing-md);
+}
+
+.empty-state h3 {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-text);
+}
+
+.empty-state p {
+  margin: 0;
+  font-size: var(--font-size-base);
+}
+
+/* Notification Items */
+.notification-items {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-md);
+}
+
+.notification-item {
+  display: flex;
+  gap: var(--spacing-md);
+  padding: var(--spacing-lg);
+  background: var(--color-surface);
+  border-radius: var(--border-radius-md);
+  border-left: 4px solid var(--color-border);
+  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
+  transition: all 0.3s ease;
+  cursor: pointer;
+}
+
+.notification-item:hover {
+  transform: translateY(-2px);
+  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
+}
+
+.notification-item.unread {
+  background: linear-gradient(135deg, rgba(0, 117, 194, 0.02) 0%, rgba(0, 117, 194, 0.01) 100%);
+  border-left-color: var(--color-primary);
+}
+
+.notification-item.critical {
+  border-left-color: var(--color-danger);
+}
+
+.notification-item.critical.unread {
+  background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.02) 100%);
+}
+
+.notification-item.warning {
+  border-left-color: var(--color-warning);
+}
+
+.notification-item.warning.unread {
+  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0.02) 100%);
+}
+
+.notification-item.success {
+  border-left-color: var(--color-success);
+}
+
+.notification-item.success.unread {
+  background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.02) 100%);
+}
+
+.notification-item.info {
+  border-left-color: var(--color-info);
+}
+
+.notification-item.info.unread {
+  background: linear-gradient(135deg, rgba(23, 162, 184, 0.05) 0%, rgba(23, 162, 184, 0.02) 100%);
+}
+
+.notification-icon {
+  flex-shrink: 0;
+  width: 40px;
+  height: 40px;
+  display: flex;
+  align-items: center;
+  justify-content: center;
+  border-radius: 50%;
+  background: var(--color-background);
+  font-size: var(--font-size-lg);
+}
+
+.notification-content {
+  flex: 1;
+  min-width: 0;
+}
+
+.notification-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: flex-start;
+  margin-bottom: var(--spacing-xs);
+  gap: var(--spacing-md);
+}
+
+.notification-title {
+  margin: 0;
+  font-size: var(--font-size-base);
+  font-weight: var(--font-weight-semibold);
+  color: var(--color-text);
+  line-height: 1.4;
+}
+
+.notification-time {
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+  white-space: nowrap;
+  flex-shrink: 0;
+}
+
+.notification-message {
+  margin: 0 0 var(--spacing-sm) 0;
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+  line-height: 1.5;
+}
+
+.notification-actions {
+  display: flex;
+  gap: var(--spacing-sm);
+  flex-wrap: wrap;
+}
+
+.action-btn {
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  font-size: var(--font-size-xs);
+  font-weight: var(--font-weight-medium);
+  border: 1px solid transparent;
+  cursor: pointer;
+  transition: all 0.2s ease;
+}
+
+.action-btn.primary {
+  background: var(--color-primary);
+  color: white;
+}
+
+.action-btn.primary:hover {
+  background: var(--color-primary-dark);
+  transform: translateY(-1px);
+}
+
+.action-btn.secondary {
+  background: var(--color-background);
+  color: var(--color-text-secondary);
+  border-color: var(--color-border);
+}
+
+.action-btn.secondary:hover {
+  background: var(--color-border);
+  color: var(--color-text);
+  transform: translateY(-1px);
+}
+
+.notification-controls {
+  flex-shrink: 0;
+  display: flex;
+  align-items: flex-start;
+}
+
+.dismiss-btn {
+  width: 24px;
+  height: 24px;
+  border-radius: 50%;
+  background: transparent;
+  border: none;
+  color: var(--color-text-secondary);
+  cursor: pointer;
+  display: flex;
+  align-items: center;
+  justify-content: center;
+  transition: all 0.2s ease;
+  font-size: var(--font-size-lg);
+}
+
+.dismiss-btn:hover {
+  background: var(--color-danger);
+  color: white;
+  transform: scale(1.1);
+}
+
+/* Mobile Responsiveness */
+@media (max-width: 768px) {
+  .notifications-container {
+    padding: var(--spacing-md);
+  }
+
+  .notifications-header {
+    flex-direction: column;
+    gap: var(--spacing-lg);
+    text-align: center;
+    padding: var(--spacing-lg);
+  }
+
+  .header-content h1 {
+    font-size: var(--font-size-xl);
+  }
+
+  .header-actions {
+    justify-content: center;
+    flex-wrap: wrap;
+  }
+
+  .notification-filters {
+    flex-wrap: wrap;
+    gap: var(--spacing-xs);
+  }
+
+  .notification-item {
+    padding: var(--spacing-md);
+  }
+
+  .notification-header {
+    flex-direction: column;
+    align-items: flex-start;
+    gap: var(--spacing-xs);
+  }
+
+  .notification-time {
+    align-self: flex-end;
+  }
+
+  .notification-actions {
+    gap: var(--spacing-xs);
+  }
+}
+
+@media (max-width: 480px) {
+  .notifications-header {
+    padding: var(--spacing-md);
+  }
+
+  .header-content h1 {
+    font-size: var(--font-size-lg);
+  }
+
+  .header-subtitle {
+    font-size: var(--font-size-sm);
+  }
+
+  .notification-item {
+    flex-direction: column;
+    gap: var(--spacing-sm);
+  }
+
+  .notification-icon {
+    align-self: flex-start;
+  }
+
+  .notification-controls {
+    align-self: flex-end;
+  }
 }
 </style>
\ No newline at end of file
diff --git a/src/components/OptimizationEngine.vue b/src/components/OptimizationEngine.vue
new file mode 100644
index 0000000..d1152f6
--- /dev/null
+++ b/src/components/OptimizationEngine.vue
@@ -0,0 +1,754 @@
+<template>
+  <div class="optimization-engine">
+    <h1>Schedule Optimization Engine</h1>
+    
+    <!-- Optimization Controls -->
+    <div class="optimization-controls">
+      <div class="control-section">
+        <h3>Optimization Settings</h3>
+        <div class="settings-grid">
+          <div class="setting-item">
+            <label>
+              <input 
+                type="checkbox" 
+                v-model="optimizationSettings.prioritizeSDST"
+                @change="updateSettings"
+              >
+              Prioritize SDST Reduction
+            </label>
+          </div>
+          <div class="setting-item">
+            <label>
+              <input 
+                type="checkbox" 
+                v-model="optimizationSettings.prioritizeUrgency"
+                @change="updateSettings"
+              >
+              Respect Surgery Urgency
+            </label>
+          </div>
+          <div class="setting-item">
+            <label>
+              <input 
+                type="checkbox" 
+                v-model="optimizationSettings.prioritizeResourceUtilization"
+                @change="updateSettings"
+              >
+              Optimize Resource Utilization
+            </label>
+          </div>
+          <div class="setting-item">
+            <label>
+              <input 
+                type="checkbox" 
+                v-model="optimizationSettings.allowMinorDelays"
+                @change="updateSettings"
+              >
+              Allow Minor Delays (Γëñ{{ optimizationSettings.maxDelayMinutes }} min)
+            </label>
+          </div>
+        </div>
+        
+        <div class="action-buttons">
+          <button 
+            class="run-optimization-btn"
+            @click="runOptimization"
+            :disabled="!canOptimize || isOptimizing"
+          >
+            <span v-if="isOptimizing" class="spinner"></span>
+            {{ isOptimizing ? 'Analyzing Schedule...' : 'Run Optimization' }}
+          </button>
+          
+          <button 
+            v-if="optimizationResults"
+            class="clear-results-btn"
+            @click="clearResults"
+          >
+            Clear Results
+          </button>
+        </div>
+      </div>
+    </div>
+
+    <!-- Optimization Results -->
+    <div v-if="optimizationResults" class="optimization-results">
+      <!-- Results Summary -->
+      <div class="results-summary">
+        <h3>Optimization Results</h3>
+        <div class="summary-cards">
+          <div class="summary-card">
+            <h4>Total Suggestions</h4>
+            <div class="metric-value">{{ optimizationSummary.totalSuggestions }}</div>
+          </div>
+          <div class="summary-card">
+            <h4>High Priority</h4>
+            <div class="metric-value priority-high">{{ optimizationSummary.highPriority }}</div>
+          </div>
+          <div class="summary-card">
+            <h4>Potential SDST Savings</h4>
+            <div class="metric-value">{{ potentialSavings.sdstReduction }} min</div>
+          </div>
+          <div class="summary-card">
+            <h4>Overall Impact</h4>
+            <div class="metric-value" :class="`impact-${optimizationSummary.estimatedImpact.toLowerCase()}`">
+              {{ optimizationSummary.estimatedImpact }}
+            </div>
+          </div>
+        </div>
+      </div>
+
+      <!-- Suggestions List -->
+      <div class="suggestions-section">
+        <div class="suggestions-header">
+          <h3>Optimization Suggestions</h3>
+          <div class="suggestions-actions">
+            <button @click="selectAllSuggestions" class="select-all-btn">
+              Select All
+            </button>
+            <button @click="clearAllSelections" class="clear-selection-btn">
+              Clear Selection
+            </button>
+            <button 
+              @click="applySelectedSuggestions"
+              :disabled="selectedSuggestions.length === 0"
+              class="apply-suggestions-btn"
+            >
+              Apply Selected ({{ selectedSuggestions.length }})
+            </button>
+          </div>
+        </div>
+
+        <div class="suggestions-list">
+          <div 
+            v-for="suggestion in currentSuggestions" 
+            :key="suggestion.id"
+            class="suggestion-card"
+            :class="[
+              `priority-${suggestion.priority.toLowerCase()}`,
+              { 'selected': selectedSuggestions.includes(suggestion.id) }
+            ]"
+          >
+            <div class="suggestion-header">
+              <div class="suggestion-checkbox">
+                <input 
+                  type="checkbox"
+                  :checked="selectedSuggestions.includes(suggestion.id)"
+                  @change="toggleSuggestionSelection(suggestion.id)"
+                >
+              </div>
+              <div class="suggestion-info">
+                <h4>{{ suggestion.title }}</h4>
+                <div class="suggestion-meta">
+                  <span class="category">{{ suggestion.category }}</span>
+                  <span class="priority-badge" :class="`priority-${suggestion.priority.toLowerCase()}`">
+                    {{ suggestion.priority }}
+                  </span>
+                  <span class="impact">Impact: {{ suggestion.impact }} min</span>
+                  <span class="effort">Effort: {{ suggestion.effort }}</span>
+                </div>
+              </div>
+            </div>
+            
+            <div class="suggestion-content">
+              <p class="suggestion-description">{{ suggestion.description }}</p>
+              
+              <div class="suggestion-details">
+                <div class="detail-item">
+                  <strong>Estimated Savings:</strong> {{ suggestion.estimatedSavings }}
+                </div>
+                <div v-if="suggestion.risks && suggestion.risks.length > 0" class="detail-item">
+                  <strong>Risks:</strong>
+                  <ul class="risk-list">
+                    <li v-for="risk in suggestion.risks" :key="risk">{{ risk }}</li>
+                  </ul>
+                </div>
+              </div>
+            </div>
+          </div>
+        </div>
+      </div>
+    </div>
+
+    <!-- No Results State -->
+    <div v-else-if="!isOptimizing" class="no-results">
+      <div class="no-results-content">
+        <h3>Ready to Optimize</h3>
+        <p>Click "Run Optimization" to analyze your current schedule and get intelligent suggestions for improvement.</p>
+        <div class="optimization-benefits">
+          <h4>Optimization Benefits:</h4>
+          <ul>
+            <li>Reduce Surgery-to-Surgery Transition (SDST) times</li>
+            <li>Improve operating room utilization</li>
+            <li>Resolve scheduling conflicts automatically</li>
+            <li>Balance workload across resources</li>
+            <li>Minimize patient wait times</li>
+          </ul>
+        </div>
+      </div>
+    </div>
+
+    <!-- Loading State -->
+    <div v-if="isOptimizing" class="optimization-loading">
+      <div class="loading-content">
+        <div class="loading-spinner"></div>
+        <h3>Analyzing Your Schedule</h3>
+        <p>Our optimization engine is analyzing your current schedule to identify improvement opportunities...</p>
+        <div class="analysis-steps">
+          <div class="step">Γ£ô Analyzing SDST patterns</div>
+          <div class="step">Γ£ô Evaluating resource utilization</div>
+          <div class="step">Γ£ô Detecting scheduling conflicts</div>
+          <div class="step">ΓÅ│ Generating optimization suggestions</div>
+        </div>
+      </div>
+    </div>
+
+    <!-- Optimization History -->
+    <div v-if="optimizationHistory.length > 0" class="optimization-history">
+      <h3>Recent Optimizations</h3>
+      <div class="history-list">
+        <div 
+          v-for="entry in optimizationHistory.slice(0, 5)" 
+          :key="entry.id"
+          class="history-item"
+          :class="{ 'applied': entry.applied }"
+        >
+          <div class="history-info">
+            <div class="history-date">{{ formatDate(entry.timestamp) }}</div>
+            <div class="history-details">
+              {{ entry.suggestionsCount }} suggestions ΓÇó {{ entry.potentialSavings }} min savings
+            </div>
+          </div>
+          <div class="history-status">
+            <span v-if="entry.applied" class="status-applied">Applied</span>
+            <span v-else class="status-pending">Pending</span>
+          </div>
+        </div>
+      </div>
+    </div>
+  </div>
+</template>
+
+<script setup>
+import { ref, computed, onMounted } from 'vue';
+import { useOptimizationStore } from '@/stores/optimizationStore';
+import { storeToRefs } from 'pinia';
+
+const optimizationStore = useOptimizationStore();
+const {
+  isOptimizing,
+  optimizationResults,
+  selectedSuggestions,
+  optimizationSettings,
+  optimizationHistory,
+  currentSuggestions,
+  potentialSavings,
+  optimizationSummary,
+  canOptimize
+} = storeToRefs(optimizationStore);
+
+// Local reactive data
+const showAdvancedSettings = ref(false);
+
+// Methods
+const runOptimization = async () => {
+  try {
+    await optimizationStore.runOptimization();
+  } catch (error) {
+    console.error('Optimization failed:', error);
+    // Could show a toast notification here
+  }
+};
+
+const clearResults = () => {
+  optimizationStore.clearOptimizationResults();
+};
+
+const updateSettings = () => {
+  optimizationStore.updateOptimizationSettings(optimizationSettings.value);
+};
+
+const toggleSuggestionSelection = (suggestionId) => {
+  optimizationStore.toggleSuggestionSelection(suggestionId);
+};
+
+const selectAllSuggestions = () => {
+  optimizationStore.selectAllSuggestions();
+};
+
+const clearAllSelections = () => {
+  optimizationStore.clearAllSelections();
+};
+
+const applySelectedSuggestions = async () => {
+  if (selectedSuggestions.value.length === 0) return;
+  
+  try {
+    await optimizationStore.applySuggestions([...selectedSuggestions.value]);
+    // Could show success notification
+    console.log('Suggestions applied successfully');
+  } catch (error) {
+    console.error('Failed to apply suggestions:', error);
+    // Could show error notification
+  }
+};
+
+const formatDate = (timestamp) => {
+  return new Date(timestamp).toLocaleString();
+};
+
+// Initialize component
+onMounted(() => {
+  // Could load any initial data here
+});
+</script>
+
+<style scoped>
+.optimization-engine {
+  padding: var(--spacing-lg);
+  max-width: 1200px;
+  margin: 0 auto;
+}
+
+h1 {
+  margin-bottom: var(--spacing-lg);
+  color: var(--color-text);
+}
+
+/* Optimization Controls */
+.optimization-controls {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.control-section h3 {
+  margin-top: 0;
+  margin-bottom: var(--spacing-md);
+  color: var(--color-text);
+}
+
+.settings-grid {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
+  gap: var(--spacing-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.setting-item label {
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-sm);
+  cursor: pointer;
+  font-size: var(--font-size-sm);
+}
+
+.setting-item input[type="checkbox"] {
+  margin: 0;
+}
+
+.action-buttons {
+  display: flex;
+  gap: var(--spacing-md);
+}
+
+.run-optimization-btn {
+  background-color: var(--color-primary);
+  color: white;
+  border: none;
+  padding: var(--spacing-md) var(--spacing-lg);
+  border-radius: var(--border-radius-sm);
+  font-weight: var(--font-weight-medium);
+  cursor: pointer;
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-sm);
+}
+
+.run-optimization-btn:disabled {
+  background-color: var(--color-border);
+  cursor: not-allowed;
+}
+
+.clear-results-btn {
+  background-color: var(--color-background-mute);
+  color: var(--color-text);
+  border: 1px solid var(--color-border);
+  padding: var(--spacing-md) var(--spacing-lg);
+  border-radius: var(--border-radius-sm);
+  cursor: pointer;
+}
+
+.spinner {
+  width: 16px;
+  height: 16px;
+  border: 2px solid rgba(255, 255, 255, 0.3);
+  border-radius: 50%;
+  border-top-color: white;
+  animation: spin 1s linear infinite;
+}
+
+@keyframes spin {
+  0% { transform: rotate(0deg); }
+  100% { transform: rotate(360deg); }
+}
+
+/* Results Summary */
+.results-summary {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.summary-cards {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
+  gap: var(--spacing-md);
+  margin-top: var(--spacing-md);
+}
+
+.summary-card {
+  background-color: var(--color-background);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  text-align: center;
+}
+
+.summary-card h4 {
+  margin: 0 0 var(--spacing-sm) 0;
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+}
+
+.metric-value {
+  font-size: 1.5rem;
+  font-weight: var(--font-weight-bold);
+  color: var(--color-text);
+}
+
+.metric-value.priority-high {
+  color: var(--color-error);
+}
+
+.metric-value.impact-high {
+  color: var(--color-success);
+}
+
+.metric-value.impact-medium {
+  color: var(--color-warning);
+}
+
+.metric-value.impact-low {
+  color: var(--color-text-secondary);
+}
+
+/* Suggestions Section */
+.suggestions-section {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.suggestions-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  margin-bottom: var(--spacing-lg);
+}
+
+.suggestions-header h3 {
+  margin: 0;
+}
+
+.suggestions-actions {
+  display: flex;
+  gap: var(--spacing-sm);
+}
+
+.suggestions-actions button {
+  padding: var(--spacing-sm) var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  border: 1px solid var(--color-border);
+  background-color: var(--color-background);
+  cursor: pointer;
+  font-size: var(--font-size-sm);
+}
+
+.apply-suggestions-btn {
+  background-color: var(--color-success) !important;
+  color: white !important;
+  border-color: var(--color-success) !important;
+}
+
+.apply-suggestions-btn:disabled {
+  background-color: var(--color-border) !important;
+  color: var(--color-text-secondary) !important;
+  cursor: not-allowed;
+}
+
+/* Suggestion Cards */
+.suggestions-list {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-md);
+}
+
+.suggestion-card {
+  background-color: var(--color-background);
+  border-radius: var(--border-radius-sm);
+  border-left: 4px solid var(--color-border);
+  padding: var(--spacing-md);
+  transition: all 0.2s ease;
+}
+
+.suggestion-card.priority-high {
+  border-left-color: var(--color-error);
+}
+
+.suggestion-card.priority-medium {
+  border-left-color: var(--color-warning);
+}
+
+.suggestion-card.priority-low {
+  border-left-color: var(--color-success);
+}
+
+.suggestion-card.selected {
+  background-color: rgba(var(--color-primary-rgb), 0.05);
+  border-color: var(--color-primary);
+}
+
+.suggestion-header {
+  display: flex;
+  align-items: flex-start;
+  gap: var(--spacing-md);
+  margin-bottom: var(--spacing-md);
+}
+
+.suggestion-checkbox input {
+  margin: 0;
+}
+
+.suggestion-info {
+  flex: 1;
+}
+
+.suggestion-info h4 {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-text);
+}
+
+.suggestion-meta {
+  display: flex;
+  gap: var(--spacing-md);
+  flex-wrap: wrap;
+}
+
+.suggestion-meta span {
+  font-size: var(--font-size-xs);
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  background-color: var(--color-background-mute);
+}
+
+.priority-badge.priority-high {
+  background-color: rgba(var(--color-error-rgb), 0.1);
+  color: var(--color-error);
+}
+
+.priority-badge.priority-medium {
+  background-color: rgba(var(--color-warning-rgb), 0.1);
+  color: var(--color-warning);
+}
+
+.priority-badge.priority-low {
+  background-color: rgba(var(--color-success-rgb), 0.1);
+  color: var(--color-success);
+}
+
+.suggestion-description {
+  margin: 0 0 var(--spacing-md) 0;
+  color: var(--color-text-secondary);
+  line-height: 1.5;
+}
+
+.suggestion-details {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-sm);
+}
+
+.detail-item {
+  font-size: var(--font-size-sm);
+}
+
+.risk-list {
+  margin: var(--spacing-xs) 0 0 var(--spacing-md);
+  padding: 0;
+}
+
+.risk-list li {
+  color: var(--color-warning);
+  margin-bottom: var(--spacing-xs);
+}
+
+/* No Results State */
+.no-results {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-xl);
+  border-radius: var(--border-radius-md);
+  text-align: center;
+}
+
+.no-results-content h3 {
+  margin-top: 0;
+  color: var(--color-text);
+}
+
+.optimization-benefits {
+  margin-top: var(--spacing-lg);
+  text-align: left;
+  max-width: 500px;
+  margin-left: auto;
+  margin-right: auto;
+}
+
+.optimization-benefits h4 {
+  margin-bottom: var(--spacing-md);
+  color: var(--color-text);
+}
+
+.optimization-benefits ul {
+  list-style-type: none;
+  padding: 0;
+}
+
+.optimization-benefits li {
+  padding: var(--spacing-xs) 0;
+  position: relative;
+  padding-left: var(--spacing-lg);
+}
+
+.optimization-benefits li::before {
+  content: 'Γ£ô';
+  position: absolute;
+  left: 0;
+  color: var(--color-success);
+  font-weight: bold;
+}
+
+/* Loading State */
+.optimization-loading {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-xl);
+  border-radius: var(--border-radius-md);
+  text-align: center;
+}
+
+.loading-content h3 {
+  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
+  color: var(--color-text);
+}
+
+.loading-spinner {
+  width: 40px;
+  height: 40px;
+  border: 4px solid rgba(var(--color-primary-rgb), 0.1);
+  border-radius: 50%;
+  border-top-color: var(--color-primary);
+  animation: spin 1s linear infinite;
+  margin: 0 auto;
+}
+
+.analysis-steps {
+  margin-top: var(--spacing-lg);
+  text-align: left;
+  max-width: 400px;
+  margin-left: auto;
+  margin-right: auto;
+}
+
+.step {
+  padding: var(--spacing-sm) 0;
+  color: var(--color-text-secondary);
+}
+
+/* Optimization History */
+.optimization-history {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+}
+
+.optimization-history h3 {
+  margin-top: 0;
+  margin-bottom: var(--spacing-md);
+}
+
+.history-list {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-sm);
+}
+
+.history-item {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  padding: var(--spacing-md);
+  background-color: var(--color-background);
+  border-radius: var(--border-radius-sm);
+}
+
+.history-item.applied {
+  border-left: 4px solid var(--color-success);
+}
+
+.history-date {
+  font-weight: var(--font-weight-medium);
+  color: var(--color-text);
+}
+
+.history-details {
+  font-size: var(--font-size-sm);
+  color: var(--color-text-secondary);
+}
+
+.status-applied {
+  color: var(--color-success);
+  font-weight: var(--font-weight-medium);
+}
+
+.status-pending {
+  color: var(--color-warning);
+  font-weight: var(--font-weight-medium);
+}
+
+/* Responsive Design */
+@media (max-width: 768px) {
+  .suggestions-header {
+    flex-direction: column;
+    align-items: stretch;
+    gap: var(--spacing-md);
+  }
+  
+  .suggestions-actions {
+    justify-content: stretch;
+  }
+  
+  .suggestions-actions button {
+    flex: 1;
+  }
+  
+  .suggestion-meta {
+    flex-direction: column;
+    gap: var(--spacing-sm);
+  }
+  
+  .summary-cards {
+    grid-template-columns: 1fr;
+  }
+}
+</style>
diff --git a/src/components/OptimizationSuggestions.vue b/src/components/OptimizationSuggestions.vue
new file mode 100644
index 0000000..f1014ef
--- /dev/null
+++ b/src/components/OptimizationSuggestions.vue
@@ -0,0 +1,436 @@
+<template>
+  <div class="optimization-suggestions">
+    <div class="suggestions-header">
+      <h3>≡ƒÜÇ Optimization Suggestions</h3>
+      <div class="header-actions">
+        <button 
+          v-if="!optimizationResults"
+          @click="runQuickOptimization"
+          :disabled="isOptimizing"
+          class="quick-optimize-btn"
+        >
+          <span v-if="isOptimizing" class="spinner"></span>
+          {{ isOptimizing ? 'Analyzing...' : 'Quick Optimize' }}
+        </button>
+        <router-link to="/optimization" class="view-all-link">
+          View All
+        </router-link>
+      </div>
+    </div>
+
+    <!-- Loading State -->
+    <div v-if="isOptimizing" class="loading-state">
+      <div class="loading-spinner"></div>
+      <p>Analyzing schedule for optimization opportunities...</p>
+    </div>
+
+    <!-- Suggestions List -->
+    <div v-else-if="topSuggestions.length > 0" class="suggestions-list">
+      <div 
+        v-for="suggestion in topSuggestions" 
+        :key="suggestion.id"
+        class="suggestion-item"
+        :class="`priority-${suggestion.priority.toLowerCase()}`"
+      >
+        <div class="suggestion-content">
+          <div class="suggestion-header">
+            <h4>{{ suggestion.title }}</h4>
+            <div class="suggestion-meta">
+              <span class="priority-badge" :class="`priority-${suggestion.priority.toLowerCase()}`">
+                {{ suggestion.priority }}
+              </span>
+              <span class="impact">{{ suggestion.impact }} min</span>
+            </div>
+          </div>
+          <p class="suggestion-description">{{ suggestion.description }}</p>
+          <div class="suggestion-actions">
+            <button 
+              @click="applySuggestion(suggestion.id)"
+              class="apply-btn"
+              :disabled="isApplying"
+            >
+              Apply
+            </button>
+            <span class="savings">{{ suggestion.estimatedSavings }}</span>
+          </div>
+        </div>
+      </div>
+    </div>
+
+    <!-- No Suggestions State -->
+    <div v-else-if="optimizationResults && topSuggestions.length === 0" class="no-suggestions">
+      <div class="no-suggestions-content">
+        <span class="success-icon">Γ£à</span>
+        <h4>Schedule Optimized!</h4>
+        <p>Your current schedule is already well-optimized. No immediate improvements found.</p>
+      </div>
+    </div>
+
+    <!-- Initial State -->
+    <div v-else class="initial-state">
+      <div class="initial-content">
+        <span class="optimize-icon">≡ƒÄ»</span>
+        <h4>Ready to Optimize</h4>
+        <p>Click "Quick Optimize" to get intelligent suggestions for improving your schedule.</p>
+        <ul class="benefits-list">
+          <li>Reduce SDST times</li>
+          <li>Improve resource utilization</li>
+          <li>Resolve conflicts</li>
+        </ul>
+      </div>
+    </div>
+  </div>
+</template>
+
+<script setup>
+import { computed, ref } from 'vue';
+import { useOptimizationStore } from '@/stores/optimizationStore';
+import { storeToRefs } from 'pinia';
+
+const optimizationStore = useOptimizationStore();
+const {
+  isOptimizing,
+  optimizationResults,
+  currentSuggestions
+} = storeToRefs(optimizationStore);
+
+// Local state
+const isApplying = ref(false);
+
+// Computed properties
+const topSuggestions = computed(() => {
+  return currentSuggestions.value.slice(0, 3); // Show top 3 suggestions
+});
+
+// Methods
+const runQuickOptimization = async () => {
+  try {
+    await optimizationStore.runOptimization();
+  } catch (error) {
+    console.error('Quick optimization failed:', error);
+  }
+};
+
+const applySuggestion = async (suggestionId) => {
+  isApplying.value = true;
+  try {
+    await optimizationStore.applySuggestions([suggestionId]);
+    console.log('Suggestion applied successfully');
+  } catch (error) {
+    console.error('Failed to apply suggestion:', error);
+  } finally {
+    isApplying.value = false;
+  }
+};
+</script>
+
+<style scoped>
+.optimization-suggestions {
+  background-color: var(--color-background-soft);
+  border-radius: var(--border-radius-md);
+  padding: var(--spacing-lg);
+  margin-bottom: var(--spacing-lg);
+}
+
+.suggestions-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  margin-bottom: var(--spacing-md);
+}
+
+.suggestions-header h3 {
+  margin: 0;
+  color: var(--color-text);
+  font-size: var(--font-size-lg);
+}
+
+.header-actions {
+  display: flex;
+  gap: var(--spacing-sm);
+  align-items: center;
+}
+
+.quick-optimize-btn {
+  background-color: var(--color-primary);
+  color: white;
+  border: none;
+  padding: var(--spacing-sm) var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  cursor: pointer;
+  font-size: var(--font-size-sm);
+  display: flex;
+  align-items: center;
+  gap: var(--spacing-xs);
+}
+
+.quick-optimize-btn:disabled {
+  background-color: var(--color-border);
+  cursor: not-allowed;
+}
+
+.view-all-link {
+  color: var(--color-primary);
+  text-decoration: none;
+  font-size: var(--font-size-sm);
+  font-weight: var(--font-weight-medium);
+}
+
+.view-all-link:hover {
+  text-decoration: underline;
+}
+
+.spinner {
+  width: 12px;
+  height: 12px;
+  border: 2px solid rgba(255, 255, 255, 0.3);
+  border-radius: 50%;
+  border-top-color: white;
+  animation: spin 1s linear infinite;
+}
+
+@keyframes spin {
+  0% { transform: rotate(0deg); }
+  100% { transform: rotate(360deg); }
+}
+
+/* Loading State */
+.loading-state {
+  text-align: center;
+  padding: var(--spacing-xl);
+}
+
+.loading-spinner {
+  width: 32px;
+  height: 32px;
+  border: 3px solid rgba(var(--color-primary-rgb), 0.1);
+  border-radius: 50%;
+  border-top-color: var(--color-primary);
+  animation: spin 1s linear infinite;
+  margin: 0 auto var(--spacing-md) auto;
+}
+
+.loading-state p {
+  color: var(--color-text-secondary);
+  margin: 0;
+}
+
+/* Suggestions List */
+.suggestions-list {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-md);
+}
+
+.suggestion-item {
+  background-color: var(--color-background);
+  border-radius: var(--border-radius-sm);
+  border-left: 4px solid var(--color-border);
+  padding: var(--spacing-md);
+}
+
+.suggestion-item.priority-high {
+  border-left-color: var(--color-error);
+}
+
+.suggestion-item.priority-medium {
+  border-left-color: var(--color-warning);
+}
+
+.suggestion-item.priority-low {
+  border-left-color: var(--color-success);
+}
+
+.suggestion-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: flex-start;
+  margin-bottom: var(--spacing-sm);
+}
+
+.suggestion-header h4 {
+  margin: 0;
+  color: var(--color-text);
+  font-size: var(--font-size-md);
+  flex: 1;
+}
+
+.suggestion-meta {
+  display: flex;
+  gap: var(--spacing-sm);
+  align-items: center;
+}
+
+.priority-badge {
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  font-size: var(--font-size-xs);
+  font-weight: var(--font-weight-medium);
+  text-transform: uppercase;
+}
+
+.priority-badge.priority-high {
+  background-color: rgba(var(--color-error-rgb), 0.1);
+  color: var(--color-error);
+}
+
+.priority-badge.priority-medium {
+  background-color: rgba(var(--color-warning-rgb), 0.1);
+  color: var(--color-warning);
+}
+
+.priority-badge.priority-low {
+  background-color: rgba(var(--color-success-rgb), 0.1);
+  color: var(--color-success);
+}
+
+.impact {
+  font-size: var(--font-size-xs);
+  color: var(--color-text-secondary);
+  background-color: var(--color-background-mute);
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+}
+
+.suggestion-description {
+  margin: 0 0 var(--spacing-md) 0;
+  color: var(--color-text-secondary);
+  font-size: var(--font-size-sm);
+  line-height: 1.4;
+}
+
+.suggestion-actions {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+}
+
+.apply-btn {
+  background-color: var(--color-success);
+  color: white;
+  border: none;
+  padding: var(--spacing-xs) var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  cursor: pointer;
+  font-size: var(--font-size-sm);
+}
+
+.apply-btn:disabled {
+  background-color: var(--color-border);
+  cursor: not-allowed;
+}
+
+.savings {
+  font-size: var(--font-size-sm);
+  color: var(--color-success);
+  font-weight: var(--font-weight-medium);
+}
+
+/* No Suggestions State */
+.no-suggestions {
+  text-align: center;
+  padding: var(--spacing-xl);
+}
+
+.no-suggestions-content {
+  max-width: 300px;
+  margin: 0 auto;
+}
+
+.success-icon {
+  font-size: 2rem;
+  display: block;
+  margin-bottom: var(--spacing-md);
+}
+
+.no-suggestions h4 {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-success);
+}
+
+.no-suggestions p {
+  margin: 0;
+  color: var(--color-text-secondary);
+  font-size: var(--font-size-sm);
+}
+
+/* Initial State */
+.initial-state {
+  text-align: center;
+  padding: var(--spacing-xl);
+}
+
+.initial-content {
+  max-width: 300px;
+  margin: 0 auto;
+}
+
+.optimize-icon {
+  font-size: 2rem;
+  display: block;
+  margin-bottom: var(--spacing-md);
+}
+
+.initial-state h4 {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-text);
+}
+
+.initial-state p {
+  margin: 0 0 var(--spacing-md) 0;
+  color: var(--color-text-secondary);
+  font-size: var(--font-size-sm);
+}
+
+.benefits-list {
+  list-style: none;
+  padding: 0;
+  margin: 0;
+  text-align: left;
+}
+
+.benefits-list li {
+  padding: var(--spacing-xs) 0;
+  color: var(--color-text-secondary);
+  font-size: var(--font-size-sm);
+  position: relative;
+  padding-left: var(--spacing-lg);
+}
+
+.benefits-list li::before {
+  content: 'Γ£ô';
+  position: absolute;
+  left: 0;
+  color: var(--color-success);
+  font-weight: bold;
+}
+
+/* Responsive Design */
+@media (max-width: 768px) {
+  .suggestions-header {
+    flex-direction: column;
+    align-items: stretch;
+    gap: var(--spacing-md);
+  }
+  
+  .header-actions {
+    justify-content: space-between;
+  }
+  
+  .suggestion-header {
+    flex-direction: column;
+    align-items: stretch;
+    gap: var(--spacing-sm);
+  }
+  
+  .suggestion-meta {
+    justify-content: flex-start;
+  }
+  
+  .suggestion-actions {
+    flex-direction: column;
+    align-items: stretch;
+    gap: var(--spacing-sm);
+  }
+}
+</style>
diff --git a/src/components/PatientManagementScreen.vue b/src/components/PatientManagementScreen.vue
index 5d19cfb..9e12a51 100644
--- a/src/components/PatientManagementScreen.vue
+++ b/src/components/PatientManagementScreen.vue
@@ -1,33 +1,83 @@
 <template>
-  <div class="patient-management-container">
-    <div v-if="!isLoading">
-      <h1>Patient Management</h1>
-      <!-- Placeholder for patient list or table -->
-      <button class="button-primary" @click="addNewPatient">Add New Patient</button>
-      <ul v-if="patients.length > 0">
-        <li v-for="patient in patients" :key="patient.id">
-          {{ patient.name }} (MRN: {{ patient.mrn }}) - DOB: {{ patient.dob }}
-        </li>
-      </ul>
-      <p v-if="patients.length === 0">No patients found.</p>
+  <div class="patient-management-screen">
+    <h1>Patient Management</h1>
+
+    <div class="toolbar">
+      <input type="text" placeholder="Search Patients (Name, MRN)..." v-model="searchTerm" class="search-input">
+      <button class="button-primary" @click="openAddPatientModal">Add New Patient</button>
+    </div>
+
+    <div v-if="isLoading" class="loading-indicator">
+      <p>Loading patient data...</p>
+      <!-- Consider adding a spinner component here -->
+    </div>
+
+    <div v-else-if="filteredPatients.length > 0" class="patient-table-container">
+      <table class="patient-table">
+        <thead>
+          <tr>
+            <th>Name</th>
+            <th>MRN</th>
+            <th>Date of Birth</th>
+            <th>Actions</th>
+          </tr>
+        </thead>
+        <tbody>
+          <tr v-for="patient in filteredPatients" :key="patient.id">
+            <td>{{ patient.name }}</td>
+            <td>{{ patient.mrn }}</td>
+            <td>{{ formatDate(patient.dob) }}</td>
+            <td>
+              <button class="button-icon button-edit" @click="editPatient(patient)" title="Edit Patient">Γ£Å∩╕Å</button>
+              <button class="button-icon button-delete" @click="confirmDeletePatient(patient)" title="Delete Patient">≡ƒùæ∩╕Å</button>
+              <!-- Add more actions like 'View Details' -->
+            </td>
+          </tr>
+        </tbody>
+      </table>
+    </div>
+
+    <div v-else-if="!isLoading && searchTerm && filteredPatients.length === 0" class="no-results">
+      <p>No patients found matching your search criteria "{{ searchTerm }}".</p>
     </div>
 
-    <div v-if="isLoading" class="loading-message">Loading patient data...</div>
+    <div v-else class="no-patients">
+      <p>No patients found. Click "Add New Patient" to get started.</p>
+    </div>
+
+    <!-- Modals for Add/Edit and Confirmation -->
+    <!-- <AddEditPatientModal v-if="showAddEditModal" :patient="selectedPatient" @close="closeAddEditModal" @save="handleSavePatient" /> -->
+    <!-- <ConfirmationModal v-if="showDeleteConfirmModal" @confirm="deletePatientConfirmed" @cancel="closeDeleteConfirmModal" title="Confirm Deletion"> -->
+      <!-- <p>Are you sure you want to delete patient {{ patientToDelete?.name }}?</p> -->
+    <!-- </ConfirmationModal> -->
 
   </div>
 </template>
 
 <script setup>
-import { ref, onMounted } from 'vue';
-
-// Script logic will be added here later
+import { ref, onMounted, computed } from 'vue';
+// import AddEditPatientModal from './modals/AddEditPatientModal.vue'; // Placeholder for modal component
+// import ConfirmationModal from './modals/ConfirmationModal.vue'; // Placeholder for modal component
 
 const isLoading = ref(true);
+const patients = ref([]);
+const searchTerm = ref('');
+
+// const showAddEditModal = ref(false);
+// const selectedPatient = ref(null);
+// const showDeleteConfirmModal = ref(false);
+// const patientToDelete = ref(null);
 
+// Simulate fetching patient data
 const fetchPatientsData = async () => {
-  // Simulate data fetching delay
-  await new Promise(resolve => setTimeout(resolve, 1000));
-  // In a real app, fetch data from backend and populate `patients.value`
+  isLoading.value = true;
+  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
+  patients.value = [
+    { id: 1, name: 'Alice Wonderland Smith', dob: '1990-05-15', mrn: 'MRN12345', gender: 'Female', contact: '555-1234', address: '123 Fantasy Lane' },
+    { id: 2, name: 'Robert "Bob" Johnson Jr.', dob: '1985-11-20', mrn: 'MRN67890', gender: 'Male', contact: '555-5678', address: '456 Reality Ave' },
+    { id: 3, name: 'Charles "Charlie" Brown III', dob: '2000-01-01', mrn: 'MRN11223', gender: 'Male', contact: '555-9012', address: '789 Comic Strip' },
+    { id: 4, name: 'Diana Prince', dob: '1975-03-22', mrn: 'MRN44556', gender: 'Female', contact: '555-3456', address: 'Themyscira Island' },
+  ];
   isLoading.value = false;
 };
 
@@ -35,25 +85,182 @@ onMounted(() => {
   fetchPatientsData();
 });
 
-const addNewPatient = () => {
-  console.log('Add New Patient button clicked');
-  // TODO: Navigate to patient creation form or open a modal
+const filteredPatients = computed(() => {
+  if (!searchTerm.value) {
+    return patients.value;
+  }
+  const lowerSearchTerm = searchTerm.value.toLowerCase();
+  return patients.value.filter(patient =>
+    patient.name.toLowerCase().includes(lowerSearchTerm) ||
+    patient.mrn.toLowerCase().includes(lowerSearchTerm)
+  );
+});
+
+const formatDate = (dateString) => {
+  if (!dateString) return '';
+  const options = { year: 'numeric', month: 'long', day: 'numeric' };
+  return new Date(dateString).toLocaleDateString(undefined, options);
 };
 
-const patients = ref([
-    { id: 1, name: 'Alice Smith', dob: '1990-05-15', mrn: 'MRN12345' },
-    { id: 2, name: 'Bob Johnson', dob: '1985-11-20', mrn: 'MRN67890' },
-    { id: 3, name: 'Charlie Brown', dob: '2000-01-01', mrn: 'MRN11223' },
-]);
+const openAddPatientModal = () => {
+  // selectedPatient.value = null; // For a new patient
+  // showAddEditModal.value = true;
+  console.log('Open Add Patient Modal');
+  // TODO: Implement actual modal opening
+};
+
+const editPatient = (patient) => {
+  // selectedPatient.value = { ...patient }; // Pass a copy to avoid direct mutation
+  // showAddEditModal.value = true;
+  console.log('Edit patient:', patient);
+  // TODO: Implement actual modal opening for editing
+};
+
+const confirmDeletePatient = (patient) => {
+  // patientToDelete.value = patient;
+  // showDeleteConfirmModal.value = true;
+  console.log('Confirm delete patient:', patient);
+  // TODO: Implement actual confirmation modal
+};
+
+// const closeAddEditModal = () => {
+//   showAddEditModal.value = false;
+//   selectedPatient.value = null;
+// };
+
+// const handleSavePatient = (savedPatient) => {
+//   if (savedPatient.id) { // Existing patient
+//     const index = patients.value.findIndex(p => p.id === savedPatient.id);
+//     if (index !== -1) patients.value.splice(index, 1, savedPatient);
+//   } else { // New patient
+//     savedPatient.id = Date.now(); // Simple ID generation for demo
+//     patients.value.push(savedPatient);
+//   }
+//   closeAddEditModal();
+//   // TODO: Add API call to save patient to backend
+//   console.log('Patient saved:', savedPatient);
+// };
+
+// const deletePatientConfirmed = () => {
+//   if (patientToDelete.value) {
+//     patients.value = patients.value.filter(p => p.id !== patientToDelete.value.id);
+//     // TODO: Add API call to delete patient from backend
+//     console.log('Patient deleted:', patientToDelete.value);
+//   }
+//   closeDeleteConfirmModal();
+// };
+
+// const closeDeleteConfirmModal = () => {
+//   showDeleteConfirmModal.value = false;
+//   patientToDelete.value = null;
+// };
+
 </script>
 
 <style scoped>
-.patient-management-container {
+.patient-management-screen {
   padding: 20px;
+  font-family: Arial, sans-serif;
+}
+
+.patient-management-screen h1 {
+  color: #333;
+  margin-bottom: 20px;
+  border-bottom: 2px solid #eee;
+  padding-bottom: 10px;
+}
+
+.toolbar {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  margin-bottom: 20px;
 }
-.loading-message {
+
+.search-input {
+  padding: 8px 12px;
+  border: 1px solid #ccc;
+  border-radius: 4px;
+  font-size: 14px;
+  width: 300px; /* Adjust as needed */
+}
+
+.button-primary {
+  background-color: #007bff;
+  color: white;
+  border: none;
+  padding: 10px 15px;
+  border-radius: 4px;
+  cursor: pointer;
+  font-size: 14px;
+  transition: background-color 0.3s ease;
+}
+
+.button-primary:hover {
+  background-color: #0056b3;
+}
+
+.loading-indicator,
+.no-results,
+.no-patients {
   text-align: center;
-  color: gray;
+  color: #666;
+  margin-top: 30px;
+  font-size: 1.1em;
+}
+
+.patient-table-container {
+  overflow-x: auto; /* For responsiveness on smaller screens */
+}
+
+.patient-table {
+  width: 100%;
+  border-collapse: collapse;
   margin-top: 20px;
+  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
 }
+
+.patient-table th,
+.patient-table td {
+  border: 1px solid #ddd;
+  padding: 10px 12px;
+  text-align: left;
+  font-size: 14px;
+}
+
+.patient-table th {
+  background-color: #f8f9fa;
+  color: #333;
+  font-weight: bold;
+}
+
+.patient-table tbody tr:nth-child(even) {
+  background-color: #f9f9f9;
+}
+
+.patient-table tbody tr:hover {
+  background-color: #f1f1f1;
+}
+
+.button-icon {
+  background: none;
+  border: none;
+  cursor: pointer;
+  font-size: 18px; /* Adjust icon size */
+  padding: 5px;
+  margin-right: 8px;
+  color: #007bff;
+  transition: color 0.2s ease;
+}
+
+.button-icon:hover {
+  color: #0056b3;
+}
+
+.button-delete:hover {
+  color: #dc3545; /* Red for delete actions */
+}
+
+/* Add styles for modals if/when implemented */
+
 </style>
\ No newline at end of file
diff --git a/src/components/SchedulingEfficiencyReports.vue b/src/components/SchedulingEfficiencyReports.vue
index 6cd6b9c..6b279b0 100644
--- a/src/components/SchedulingEfficiencyReports.vue
+++ b/src/components/SchedulingEfficiencyReports.vue
@@ -1,7 +1,7 @@
 <template>
   <div class="efficiency-reports">
     <h1>Scheduling Efficiency Reports</h1>
-    
+
     <!-- Report Controls -->
     <div class="report-controls">
       <div class="filter-section">
@@ -15,7 +15,7 @@
             <option value="overtime">Overtime Analysis</option>
           </select>
         </div>
-        
+
         <div class="filter-group">
           <label for="date-range">Date Range</label>
           <select id="date-range" v-model="selectedDateRange">
@@ -26,62 +26,103 @@
             <option value="custom">Custom Range</option>
           </select>
         </div>
-        
+
         <div v-if="selectedDateRange === 'custom'" class="custom-date-range">
           <div class="date-input">
             <label for="start-date">Start Date</label>
-            <input 
-              type="date" 
-              id="start-date" 
+            <input
+              type="date"
+              id="start-date"
               v-model="customDateRange.start"
             >
           </div>
           <div class="date-input">
             <label for="end-date">End Date</label>
-            <input 
-              type="date" 
-              id="end-date" 
+            <input
+              type="date"
+              id="end-date"
               v-model="customDateRange.end"
             >
           </div>
         </div>
-        
+
         <button class="apply-button" @click="applyFilters">Apply Filters</button>
       </div>
     </div>
-    
+
     <!-- Loading Indicator -->
     <div v-if="isLoading" class="loading-overlay">
       <div class="spinner"></div>
       <p>Loading report data...</p>
     </div>
-    
+
     <!-- Report Content -->
     <div v-else class="report-content">
       <h2>{{ reportTitle }}</h2>
-      
+
       <!-- SDST Efficiency Report -->
       <div v-if="selectedMetricType === 'sdst'" class="sdst-efficiency">
         <div class="summary-cards">
           <div class="metric-card">
             <h3>Average SDST</h3>
-            <div class="metric-value">{{ sdstData.averageSDST }} min</div>
+            <div class="metric-value">{{ Math.round(sdstEfficiencyData?.averageSDST || 22.5) }} min</div>
             <div class="metric-description">Average setup time between surgeries</div>
           </div>
-          
+
           <div class="metric-card">
             <h3>SDST % of OR Time</h3>
-            <div class="metric-value">{{ formatPercentage(sdstData.sdstPercentage) }}</div>
+            <div class="metric-value">{{ formatPercentage(sdstEfficiencyData?.sdstPercentage || 0.12) }}</div>
             <div class="metric-description">Percentage of total OR time spent on setup</div>
           </div>
-          
+
           <div class="metric-card">
             <h3>Potential Time Savings</h3>
-            <div class="metric-value">{{ sdstData.potentialSavings }} min/day</div>
+            <div class="metric-value">{{ Math.round(sdstEfficiencyData?.potentialSavings || 120) }} min/day</div>
             <div class="metric-description">Estimated time that could be saved with optimal scheduling</div>
           </div>
+
+          <div class="metric-card">
+            <h3>Optimization Opportunities</h3>
+            <div class="metric-value">{{ optimizationCount }}</div>
+            <div class="metric-description">Identified improvement areas</div>
+          </div>
+        </div>
+
+        <!-- SDST Patterns Analysis -->
+        <div v-if="sdstPatterns?.transitionMatrix" class="sdst-patterns">
+          <h3>SDST Transition Analysis</h3>
+          <div class="patterns-grid">
+            <div class="pattern-card">
+              <h4>Time of Day Patterns</h4>
+              <div v-if="sdstPatterns.timeOfDayPatterns" class="time-patterns">
+                <div class="time-slot">
+                  <span class="time-label">Morning</span>
+                  <span class="time-value">{{ Math.round(sdstPatterns.timeOfDayPatterns.morning?.avgSDST || 0) }} min avg</span>
+                </div>
+                <div class="time-slot">
+                  <span class="time-label">Afternoon</span>
+                  <span class="time-value">{{ Math.round(sdstPatterns.timeOfDayPatterns.afternoon?.avgSDST || 0) }} min avg</span>
+                </div>
+                <div class="time-slot">
+                  <span class="time-label">Evening</span>
+                  <span class="time-value">{{ Math.round(sdstPatterns.timeOfDayPatterns.evening?.avgSDST || 0) }} min avg</span>
+                </div>
+              </div>
+            </div>
+
+            <div class="pattern-card">
+              <h4>OR-Specific Performance</h4>
+              <div v-if="sdstPatterns.orSpecificPatterns" class="or-patterns">
+                <div v-for="(pattern, orId) in Object.entries(sdstPatterns.orSpecificPatterns).slice(0, 3)"
+                     :key="orId" class="or-performance">
+                  <span class="or-label">{{ pattern[1].orName || `OR ${pattern[0]}` }}</span>
+                  <span class="or-value">{{ Math.round(pattern[1].avgSDST || 0) }} min avg</span>
+                </div>
+              </div>
+            </div>
+          </div>
         </div>
-        
+
         <div class="efficiency-tables">
           <div class="efficiency-table">
             <h3>Most Efficient Transitions</h3>
@@ -91,28 +132,20 @@
                   <th>From</th>
                   <th>To</th>
                   <th>Avg. Time</th>
+                  <th>Count</th>
                 </tr>
               </thead>
               <tbody>
-                <tr>
-                  <td>APPEN</td>
-                  <td>KNEE</td>
-                  <td>15 min</td>
-                </tr>
-                <tr>
-                  <td>KNEE</td>
-                  <td>HIPRE</td>
-                  <td>15 min</td>
-                </tr>
-                <tr>
-                  <td>HERNI</td>
-                  <td>APPEN</td>
-                  <td>15 min</td>
+                <tr v-for="transition in mostEfficientTransitions" :key="`${transition.from}-${transition.to}`">
+                  <td>{{ transition.from }}</td>
+                  <td>{{ transition.to }}</td>
+                  <td>{{ transition.time }} min</td>
+                  <td>{{ transition.count || 'N/A' }}</td>
                 </tr>
               </tbody>
             </table>
           </div>
-          
+
           <div class="efficiency-table">
             <h3>Least Efficient Transitions</h3>
             <table>
@@ -121,57 +154,64 @@
                   <th>From</th>
                   <th>To</th>
                   <th>Avg. Time</th>
+                  <th>Count</th>
                 </tr>
               </thead>
               <tbody>
-                <tr>
-                  <td>CABG</td>
-                  <td>APPEN</td>
-                  <td>45 min</td>
-                </tr>
-                <tr>
-                  <td>CABG</td>
-                  <td>CATAR</td>
-                  <td>45 min</td>
-                </tr>
-                <tr>
-                  <td>HIPRE</td>
-                  <td>CABG</td>
-                  <td>45 min</td>
+                <tr v-for="transition in leastEfficientTransitions" :key="`${transition.from}-${transition.to}`">
+                  <td>{{ transition.from }}</td>
+                  <td>{{ transition.to }}</td>
+                  <td>{{ transition.time }} min</td>
+                  <td>{{ transition.count || 'N/A' }}</td>
                 </tr>
               </tbody>
             </table>
           </div>
         </div>
-        
+
         <div class="recommendations">
-          <h3>Recommendations</h3>
-          <ul>
+          <h3>Optimization Recommendations</h3>
+          <div v-if="sdstPatterns?.optimizationOpportunities" class="recommendations-grid">
+            <div v-for="opportunity in sdstPatterns.optimizationOpportunities"
+                 :key="opportunity.type"
+                 class="recommendation-card"
+                 :class="`priority-${opportunity.priority.toLowerCase()}`">
+              <div class="recommendation-header">
+                <h4>{{ opportunity.type }}</h4>
+                <span class="priority-badge">{{ opportunity.priority }}</span>
+              </div>
+              <p>{{ opportunity.description }}</p>
+              <div class="potential-savings">
+                <strong>Potential Savings: {{ opportunity.potentialSavings }}</strong>
+              </div>
+            </div>
+          </div>
+          <ul v-else>
             <li>Group similar surgery types together to minimize SDST</li>
             <li>Schedule CABG procedures at the end of the day when possible</li>
             <li>Consider dedicated ORs for specific surgery types to reduce setup times</li>
           </ul>
         </div>
       </div>
-      
+
       <!-- Turnaround Time Report -->
       <div v-else-if="selectedMetricType === 'turnaround'" class="turnaround-time">
         <!-- Turnaround time report content would go here -->
         <p>Turnaround Time Report content will be implemented in the next phase.</p>
       </div>
-      
+
       <!-- On-Time Start Rate Report -->
       <div v-else-if="selectedMetricType === 'ontime'" class="ontime-start">
         <!-- On-time start report content would go here -->
         <p>On-Time Start Rate Report content will be implemented in the next phase.</p>
       </div>
-      
+
       <!-- Overtime Analysis Report -->
       <div v-else-if="selectedMetricType === 'overtime'" class="overtime-analysis">
         <!-- Overtime analysis report content would go here -->
         <p>Overtime Analysis Report content will be implemented in the next phase.</p>
       </div>
-      
+
       <!-- Export Options -->
       <div class="export-options">
         <button @click="exportToPDF">Export to PDF</button>
@@ -188,7 +228,14 @@ import { useAnalyticsStore } from '@/stores/analyticsStore';
 import { storeToRefs } from 'pinia';
 
 const analyticsStore = useAnalyticsStore();
-const { isLoading, error } = storeToRefs(analyticsStore);
+const {
+  isLoading,
+  error,
+  schedulingEfficiency,
+  sdstPatterns,
+  conflictAnalysis,
+  resourceOptimization
+} = storeToRefs(analyticsStore);
 
 // Filter state
 const selectedMetricType = ref('sdst');
@@ -231,6 +278,38 @@ const reportTitle = computed(() => {
   }
 });
 
+const sdstEfficiencyData = computed(() => {
+  return analyticsStore.sdstEfficiency || sdstData.value;
+});
+
+const optimizationCount = computed(() => {
+  return sdstPatterns.value?.optimizationOpportunities?.length || 3;
+});
+
+const mostEfficientTransitions = computed(() => {
+  if (!sdstPatterns.value?.transitionMatrix) return sdstData.value.mostEfficientTransitions;
+
+  return Object.entries(sdstPatterns.value.transitionMatrix)
+    .sort((a, b) => a[1].avgTime - b[1].avgTime)
+    .slice(0, 3)
+    .map(([transition, data]) => {
+      const [from, to] = transition.split('->');
+      return { from, to, time: Math.round(data.avgTime) };
+    });
+});
+
+const leastEfficientTransitions = computed(() => {
+  if (!sdstPatterns.value?.transitionMatrix) return sdstData.value.leastEfficientTransitions;
+
+  return Object.entries(sdstPatterns.value.transitionMatrix)
+    .sort((a, b) => b[1].avgTime - a[1].avgTime)
+    .slice(0, 3)
+    .map(([transition, data]) => {
+      const [from, to] = transition.split('->');
+      return { from, to, time: Math.round(data.avgTime) };
+    });
+});
+
 // Methods
 const formatPercentage = (value) => {
   return `${Math.round(value * 100)}%`;
@@ -243,13 +322,13 @@ const applyFilters = async () => {
     dateRange: selectedDateRange.value,
     customDateRange: customDateRange.value
   });
-  
-  // Simulate loading
-  isLoading.value = true;
-  await new Promise(resolve => setTimeout(resolve, 1000));
-  isLoading.value = false;
-  
-  // In a real app, we would update the report data here
+
+  try {
+    // Load enhanced analytics data
+    await analyticsStore.loadAnalyticsData();
+  } catch (error) {
+    console.error('Failed to load analytics data:', error);
+  }
 };
 
 const exportToPDF = () => {
@@ -273,10 +352,10 @@ onMounted(async () => {
   const today = new Date();
   const thirtyDaysAgo = new Date(today);
   thirtyDaysAgo.setDate(today.getDate() - 30);
-  
+
   customDateRange.value.start = thirtyDaysAgo.toISOString().split('T')[0];
   customDateRange.value.end = today.toISOString().split('T')[0];
-  
+
   // Load initial data
   await applyFilters();
 });
@@ -432,6 +511,59 @@ select, input {
   color: var(--color-text-secondary);
 }
 
+/* SDST Patterns */
+.sdst-patterns {
+  background-color: var(--color-background-soft);
+  padding: var(--spacing-lg);
+  border-radius: var(--border-radius-md);
+  margin-bottom: var(--spacing-lg);
+}
+
+.patterns-grid {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
+  gap: var(--spacing-md);
+  margin-top: var(--spacing-md);
+}
+
+.pattern-card {
+  background-color: var(--color-background);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  border-left: 4px solid var(--color-primary);
+}
+
+.pattern-card h4 {
+  margin: 0 0 var(--spacing-sm) 0;
+  color: var(--color-text);
+  font-size: var(--font-size-md);
+}
+
+.time-patterns, .or-patterns {
+  display: flex;
+  flex-direction: column;
+  gap: var(--spacing-xs);
+}
+
+.time-slot, .or-performance {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  padding: var(--spacing-xs);
+  background-color: var(--color-background-mute);
+  border-radius: var(--border-radius-sm);
+}
+
+.time-label, .or-label {
+  font-weight: var(--font-weight-medium);
+  color: var(--color-text);
+}
+
+.time-value, .or-value {
+  font-weight: var(--font-weight-bold);
+  color: var(--color-primary);
+}
+
 .recommendations {
   background-color: var(--color-background-soft);
   padding: var(--spacing-md);
@@ -439,6 +571,74 @@ select, input {
   margin-bottom: var(--spacing-lg);
 }
 
+.recommendations-grid {
+  display: grid;
+  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
+  gap: var(--spacing-md);
+  margin-top: var(--spacing-md);
+}
+
+.recommendation-card {
+  background-color: var(--color-background);
+  padding: var(--spacing-md);
+  border-radius: var(--border-radius-sm);
+  border-left: 4px solid var(--color-border);
+}
+
+.recommendation-card.priority-high {
+  border-left-color: var(--color-error);
+}
+
+.recommendation-card.priority-medium {
+  border-left-color: var(--color-warning);
+}
+
+.recommendation-card.priority-low {
+  border-left-color: var(--color-success);
+}
+
+.recommendation-header {
+  display: flex;
+  justify-content: space-between;
+  align-items: center;
+  margin-bottom: var(--spacing-sm);
+}
+
+.recommendation-header h4 {
+  margin: 0;
+  color: var(--color-text);
+  font-size: var(--font-size-md);
+}
+
+.priority-badge {
+  padding: var(--spacing-xs) var(--spacing-sm);
+  border-radius: var(--border-radius-sm);
+  font-size: var(--font-size-xs);
+  font-weight: var(--font-weight-medium);
+  text-transform: uppercase;
+}
+
+.priority-high .priority-badge {
+  background-color: rgba(var(--color-error-rgb), 0.1);
+  color: var(--color-error);
+}
+
+.priority-medium .priority-badge {
+  background-color: rgba(var(--color-warning-rgb), 0.1);
+  color: var(--color-warning);
+}
+
+.priority-low .priority-badge {
+  background-color: rgba(var(--color-success-rgb), 0.1);
+  color: var(--color-success);
+}
+
+.potential-savings {
+  color: var(--color-success);
+  font-size: var(--font-size-sm);
+  margin-top: var(--spacing-sm);
+}
+
 .recommendations ul {
   margin: 0;
   padding-left: var(--spacing-lg);
@@ -469,7 +669,7 @@ select, input {
   .custom-date-range {
     grid-template-columns: 1fr;
   }
-  
+
   .export-options {
     flex-direction: column;
     align-items: stretch;
diff --git a/src/components/SchedulingScreen.vue b/src/components/SchedulingScreen.vue
index 893bdb6..62508ef 100644
--- a/src/components/SchedulingScreen.vue
+++ b/src/components/SchedulingScreen.vue
@@ -1,195 +1,211 @@
 <template>
   <div class="scheduling-container">
-    <!-- Toast Notifications and Keyboard Shortcuts Help -->
     <ToastNotification ref="toastRef" />
     <KeyboardShortcutsHelp ref="keyboardShortcutsRef" />
 
     <h1>Surgery Scheduling</h1>
 
     <div class="scheduling-layout">
-      <!-- Left Panel: Pending Surgeries & Filters -->
       <aside class="left-panel">
         <h2>Pending Surgeries</h2>
         <p>Drag and drop surgeries from this list onto the schedule.</p>
 
         <div class="filters-section">
-             <div class="filters-header">
-                <h3>Filters</h3>
-                <button
-                  @click="filters.showAdvancedFilters = !filters.showAdvancedFilters"
-                  class="btn btn-sm btn-link"
-                >
-                  {{ filters.showAdvancedFilters ? 'Hide Advanced' : 'Show Advanced' }}
-                </button>
-             </div>
-
-             <!-- Basic Filters -->
-             <div class="filter-group">
-                <label for="filter-priority">Priority:</label>
-                <select id="filter-priority" v-model="filters.priority" @change="applyFilters" class="form-control">
-                    <option value="">All</option>
-                    <option value="High">High</option>
-                    <option value="Medium">Medium</option>
-                    <option value="Low">Low</option>
-                </select>
-             </div>
-             <div class="filter-group">
-                <label for="filter-specialty">Specialty:</label>
-                <input type="text" id="filter-specialty" v-model="filters.specialty" placeholder="e.g., Cardiac" @input="applyFilters" class="form-control">
-             </div>
-             <div class="filter-group">
-                <label for="filter-status">Status:</label>
-                <select id="filter-status" v-model="filters.status" @change="applyFilters" class="form-control">
-                    <option value="">All</option>
-                    <option value="Pending">Pending</option>
-                    <option value="Scheduled">Scheduled</option>
-                    <option value="Cancelled">Cancelled</option>
-                </select>
-             </div>
-
-             <!-- Advanced Filters -->
-             <div v-if="filters.showAdvancedFilters" class="advanced-filters">
-                <div class="filter-group">
-                   <label for="filter-surgeon">Surgeon:</label>
-                   <input type="text" id="filter-surgeon" v-model="filters.surgeon" placeholder="e.g., Dr. Smith" @input="applyFilters" class="form-control">
-                </div>
-                <div class="filter-group">
-                   <label for="filter-equipment">Equipment:</label>
-                   <input type="text" id="filter-equipment" v-model="filters.equipment" placeholder="e.g., Heart-Lung Machine" @input="applyFilters" class="form-control">
-                </div>
-                <div class="filter-group">
-                   <label>Date Range:</label>
-                   <div class="date-range-inputs">
-                      <input
-                        type="date"
-                        v-model="filters.dateRange.start"
-                        class="form-control"
-                        @change="applyFilters"
-                      >
-                      <span class="date-range-separator">to</span>
-                      <input
-                        type="date"
-                        v-model="filters.dateRange.end"
-                        class="form-control"
-                        @change="applyFilters"
-                      >
-                   </div>
-                </div>
-             </div>
-
-             <div class="filter-actions">
-                <button @click="applyFilters" class="btn btn-sm btn-primary">Apply Filters</button>
-                <button @click="resetFilters" class="btn btn-sm btn-secondary">Reset</button>
-             </div>
-         </div>
-
-         <div class="sort-section">
-             <h3>Sort By</h3>
-             <div class="sort-controls">
-                <select v-model="sortOptions.field" class="form-control">
-                    <option value="priority">Priority</option>
-                    <option value="patientName">Patient Name</option>
-                    <option value="type">Surgery Type</option>
-                    <option value="estimatedDuration">Duration</option>
-                </select>
-                <div class="sort-direction">
-                    <button
-                        @click="sortOptions.direction = 'asc'"
-                        class="btn btn-sm"
-                        :class="{'btn-primary': sortOptions.direction === 'asc', 'btn-secondary': sortOptions.direction !== 'asc'}"
+            <div class="filters-header">
+              <h3>Filters</h3>
+              <button
+                @click="filters.showAdvancedFilters = !filters.showAdvancedFilters"
+                class="btn btn-sm btn-link"
+              >
+                {{ filters.showAdvancedFilters ? 'Hide Advanced' : 'Show Advanced' }}
+              </button>
+            </div>
+
+            <div class="filter-group">
+              <label for="filter-priority">Priority:</label>
+              <select id="filter-priority" v-model="filters.priority" @change="applyFilters" class="form-control">
+                  <option value="">All</option>
+                  <option value="High">High</option>
+                  <option value="Medium">Medium</option>
+                  <option value="Low">Low</option>
+              </select>
+            </div>
+            <div class="filter-group">
+              <label for="filter-specialty">Specialty:</label>
+              <input type="text" id="filter-specialty" v-model="filters.specialty" placeholder="e.g., Cardiac" @input="applyFilters" class="form-control">
+            </div>
+            <div class="filter-group">
+              <label for="filter-status">Status:</label>
+              <select id="filter-status" v-model="filters.status" @change="applyFilters" class="form-control">
+                  <option value="">All</option>
+                  <option value="Pending">Pending</option>
+                  <option value="Scheduled">Scheduled</option>
+                  <option value="Cancelled">Cancelled</option>
+              </select>
+            </div>
+
+            <div v-if="filters.showAdvancedFilters" class="advanced-filters">
+              <div class="filter-group">
+                  <label for="filter-surgeon">Surgeon:</label>
+                  <input type="text" id="filter-surgeon" v-model="filters.surgeon" placeholder="e.g., Dr. Smith" @input="applyFilters" class="form-control">
+              </div>
+              <div class="filter-group">
+                  <label for="filter-equipment">Equipment:</label>
+                  <input type="text" id="filter-equipment" v-model="filters.equipment" placeholder="e.g., Heart-Lung Machine" @input="applyFilters" class="form-control">
+              </div>
+              <div class="filter-group">
+                  <label>Date Range:</label>
+                  <div class="date-range-inputs">
+                    <input
+                      type="date"
+                      v-model="filters.dateRange.start"
+                      class="form-control"
+                      @change="applyFilters"
                     >
-                        Γåæ Asc
-                    </button>
-                    <button
-                        @click="sortOptions.direction = 'desc'"
-                        class="btn btn-sm"
-                        :class="{'btn-primary': sortOptions.direction === 'desc', 'btn-secondary': sortOptions.direction !== 'desc'}"
+                    <span class="date-range-separator">to</span>
+                    <input
+                      type="date"
+                      v-model="filters.dateRange.end"
+                      class="form-control"
+                      @change="applyFilters"
                     >
-                        Γåô Desc
-                    </button>
-                </div>
-             </div>
-         </div>
+                  </div>
+              </div>
+            </div>
+
+            <div class="filter-actions">
+              <button @click="applyFilters" class="btn btn-sm btn-primary">Apply Filters</button>
+              <button @click="resetFilters" class="btn btn-sm btn-secondary">Reset</button>
+            </div>
+        </div>
+
+        <div class="sort-section">
+            <h3>Sort By</h3>
+            <div class="sort-controls">
+              <select v-model="sortOptions.field" @change="applyFilters" class="form-control"> <option value="priority">Priority</option>
+                  <option value="patientName">Patient Name</option>
+                  <option value="type">Surgery Type</option>
+                  <option value="estimatedDuration">Duration</option>
+              </select>
+              <div class="sort-direction">
+                  <button
+                    @click="sortOptions.direction = 'asc'; applyFilters()" class="btn btn-sm"
+                    :class="{'btn-primary': sortOptions.direction === 'asc', 'btn-secondary': sortOptions.direction !== 'asc'}"
+                  >
+                    Γåæ Asc
+                  </button>
+                  <button
+                    @click="sortOptions.direction = 'desc'; applyFilters()" class="btn btn-sm"
+                    :class="{'btn-primary': sortOptions.direction === 'desc', 'btn-secondary': sortOptions.direction !== 'desc'}"
+                  >
+                    Γåô Desc
+                  </button>
+              </div>
+            </div>
+        </div>
 
-        <!-- TODO: Implement drag functionality for these items -->
         <div class="pending-surgeries-list">
             <ul>
                 <li
-                    v-for="surgery in filteredPendingSurgeries"
-                    :key="surgery.id"
-                    class="pending-surgery-item"
-                    :class="{
-                      'selected': selectedSurgery && selectedSurgery.id === surgery.id,
-                      [`priority-${surgery.priority.toLowerCase()}`]: true
-                    }"
-                    draggable="true"
-                    @dragstart="handleDragStart(surgery, $event)"
-                    @dragend="handleDragEnd($event)"
-                    @click="selectSurgeryForDetails(surgery, 'pending')"
+                  v-for="surgery in filteredPendingSurgeries"
+                  :key="surgery.id"
+                  class="pending-surgery-item"
+                  :class="{
+                    'selected': selectedSurgery && selectedSurgery.id === surgery.id,
+                    [`priority-${surgery.priority.toLowerCase()}`]: true
+                  }"
+                  draggable="true"
+                  @dragstart="handleDragStart(surgery, $event)"
+                  @dragend="handleDragEnd($event)"
+                  @click="selectSurgeryForDetails(surgery, 'pending')"
                 >
-                    <div class="item-header">
-                      <div class="patient-info">
-                        <span class="patient-name">{{ surgery.patientName || surgery.patientId }}</span>
-                        <span class="patient-id" v-if="surgery.patientName">({{ surgery.patientId }})</span>
-                      </div>
-                      <span class="priority-badge" :class="`priority-${surgery.priority.toLowerCase()}`">
-                        {{ surgery.priority }}
-                      </span>
+                  <div class="item-header">
+                    <div class="patient-info">
+                      <span class="patient-name">{{ surgery.patientName || surgery.patientId }}</span>
+                      <span class="patient-id" v-if="surgery.patientName">({{ surgery.patientId }})</span>
                     </div>
-
-                    <div class="item-details">
-                      <div class="surgery-type">
-                        <span class="label">Type:</span>
-                        <span class="value">{{ surgery.type }}</span>
-                      </div>
-                      <div class="surgery-full-type">
-                        <span class="value">{{ surgery.fullType }}</span>
-                      </div>
-                      <div class="surgery-duration">
-                        <span class="label">Duration:</span>
-                        <span class="value">{{ surgery.estimatedDuration }} min</span>
-                      </div>
+                    <span class="priority-badge" :class="`priority-${surgery.priority.toLowerCase()}`">
+                      {{ surgery.priority }}
+                    </span>
+                  </div>
+                  <div class="item-details">
+                    <div class="surgery-type">
+                      <span class="label">Type:</span>
+                      <span class="value">{{ surgery.type }}</span>
                     </div>
-
-                    <div class="item-status">
-                      <span class="status-indicator" :class="`status-${surgery.status?.toLowerCase() || 'pending'}`"></span>
-                      <span>{{ surgery.status || 'Pending' }}</span>
+                    <div class="surgery-full-type">
+                      <span class="value">{{ surgery.fullType }}</span>
                     </div>
-
-                    <div class="item-actions">
-                        <button class="btn btn-sm btn-secondary" @click.stop="selectSurgeryForDetails(surgery, 'pending')">
-                          <span class="icon">≡ƒæü∩╕Å</span> View
-                        </button>
-                        <button class="btn btn-sm btn-primary" @click.stop="scheduleSelectedSurgery(surgery)">
-                          <span class="icon">≡ƒôà</span> Schedule
-                        </button>
+                    <div class="surgery-duration">
+                      <span class="label">Duration:</span>
+                      <span class="value">{{ surgery.estimatedDuration }} min</span>
                     </div>
+                  </div>
+                  <div class="item-status">
+                    <span class="status-indicator" :class="`status-${surgery.status?.toLowerCase() || 'pending'}`"></span>
+                    <span>{{ surgery.status || 'Pending' }}</span>
+                  </div>
+                  <div class="item-actions">
+                      <button class="btn btn-sm btn-secondary" @click.stop="selectSurgeryForDetails(surgery, 'pending')">
+                        <span class="icon">≡ƒæü∩╕Å</span> View
+                      </button>
+                      <button class="btn btn-sm btn-primary" @click.stop="promptScheduleSurgery(surgery)"> <span class="icon">≡ƒôà</span> Schedule
+                      </button>
+                  </div>
                 </li>
                 <li v-if="filteredPendingSurgeries.length === 0" class="no-items">No pending surgeries matching filters.</li>
             </ul>
         </div>
       </aside>
 
-      <!-- Main Panel: Master Schedule View (Gantt Chart) -->
       <main class="main-panel">
         <div class="schedule-header">
             <h2>Master Schedule View</h2>
             <div class="schedule-controls">
                 <button @click="ganttNavigate('prev')" class="btn btn-sm btn-secondary">ΓùÇ Previous</button>
-                <span class="current-date-range">{{ currentGanttViewDateRange }}</span>
+                <span class="current-date-range">{{ currentGanttViewDateRangeForDisplay }}</span>
                 <button @click="ganttNavigate('next')" class="btn btn-sm btn-secondary">Next Γû╢</button>
-                <button @click="ganttZoom('in')" class="btn btn-sm btn-secondary">Day View</button>
-                <button @click="ganttZoom('out')" class="btn btn-sm btn-secondary">Week View</button>
-                <button @click="showCreateNewSurgeryForm" class="btn btn-sm btn-primary">Create New Surgery</button>
+                <button @click="ganttZoom('day')" class="btn btn-sm btn-secondary">Day View</button> <button @click="ganttZoom('week')" class="btn btn-sm btn-secondary">Week View</button> <button @click="showCreateNewSurgeryForm" class="btn btn-sm btn-primary">Create New Surgery</button>
             </div>
         </div>
 
+        <OptimizationSuggestions />
+
+        <div class="mt-8 bg-white p-4 rounded-lg shadow gantt-chart-wrapper">
+          <h3 class="text-lg font-semibold mb-4 text-gray-700">Gantt Chart (vue-ganttastic)</h3>
+          <g-gantt-chart
+            :chart-start="ganttChartStart"
+            :chart-end="ganttChartEnd"
+            precision="hour"
+            bar-start="myBeginDate"
+            bar-end="myEndDate"
+            row-label-width="150px"
+            grid-label-width="100px"
+            :grid="true"
+            :highlighted-dates="ganttHighlightedDates"
+            @click-bar="handleClickGanttBar($event.bar, $event.e, $event.datetime)"
+            @dragend-bar="handleDragEndGanttBar($event.bar, $event.e)"
+            @contextmenu-bar="handleContextmenuGanttBar($event.bar, $event.e, $event.datetime)"
+            :row-label-font="'12px sans-serif'"
+            :row-height="40"
+            :highlight-on-hover="true"
+            :push-on-overlap="false"
+            :snap-back-on-overlap="true"
+            :overlap-sensitivity="5"
+            :bar-config-key="'ganttBarConfig'"
+          >
+            <g-gantt-row
+              v-for="or in operatingRooms"
+              :key="or.id"
+              :label="or.name"
+              :bars="getBarsForRow(or.id)"
+              :highlight-on-hover="true"
+            />
+            <g-gantt-row v-if="!operatingRooms.length" label="No ORs Loaded" :bars="[]" />
+          </g-gantt-chart>
+        </div>
+
         <!--
-          Gantt Chart Integration Point
-          This div will host the Gantt chart component.
-          Consider creating a dedicated child component (e.g., <GanttChartComponent />)
-          to encapsulate the Gantt library's logic and pass data via props.
-        -->
         <div
           id="gantt-chart-container"
           class="gantt-chart-container"
@@ -211,14 +227,13 @@
             (Drop pending surgeries here to schedule)
           </div>
           <div v-else>
-            <!-- Actual Gantt Chart Component -->
             <GanttChart />
-
             <div class="gantt-drop-message">
               Drag pending surgeries here to schedule them.
             </div>
           </div>
         </div>
+        -->
 
         <div class="gantt-info-panel">
             <p><strong>SDST (Setup, Disinfection, Sterilization Time):</strong> Not yet calculated. Will be factored into scheduling.</p>
@@ -226,7 +241,6 @@
         </div>
       </main>
 
-      <!-- Right Panel: Surgery Details / Create New -->
       <aside class="right-panel">
         <div v-if="selectedSurgery">
           <h2>Surgery Details ({{ selectedSurgerySource === 'pending' ? 'Pending' : 'Scheduled' }})</h2>
@@ -329,6 +343,12 @@
             <div class="form-group" v-if="selectedSurgerySource === 'scheduled'">
               <label for="scheduledTime">Scheduled Time:</label>
               <input type="datetime-local" id="scheduledTime" v-model="selectedSurgery.scheduledTime" :disabled="formMode === 'view'" class="form-control">
+            </div>
+             <div class="form-group" v-if="selectedSurgerySource === 'scheduled'">
+              <label for="operatingRoom">Operating Room:</label>
+              <select id="operatingRoom" v-model="selectedSurgery.orId" :disabled="formMode === 'view'" class="form-control">
+                <option v-for="or in operatingRooms" :key="or.id" :value="or.id">{{ or.name }}</option>
+              </select>
             </div>
             <div class="form-group">
               <label for="status">Status:</label>
@@ -363,42 +383,15 @@
               <button type="button" v-if="formMode === 'view'" @click="formMode = 'edit'" class="btn btn-primary">Edit</button>
               <button type="submit" v-if="formMode !== 'view'" class="btn btn-primary">Save Changes</button>
               <button type="button" @click="clearSelectionOrCancel" class="btn btn-secondary">{{ formMode === 'new' ? 'Cancel' : 'Close' }}</button>
-              <button type="button" v-if="selectedSurgerySource === 'pending' && formMode !== 'new'" @click="scheduleSelectedSurgery" class="btn btn-primary">Schedule This Surgery</button>
+              <button type="button" v-if="selectedSurgerySource === 'pending' && formMode !== 'new'" @click="promptScheduleSurgery(selectedSurgery)" class="btn btn-primary">Schedule This Surgery</button>
             </div>
           </form>
         </div>
         <div v-else>
           <h2>Surgery Details</h2>
           <p>Select a pending surgery to view its details, or drag it to the schedule. Click "Create New Surgery" to add a new entry.</p>
-
-          <!-- Show form fields for reference/testing -->
           <div class="form-preview">
-            <h3>Surgery Form Fields</h3>
-            <div class="form-group">
-              <label>Patient ID:</label>
-              <input type="text" class="form-control" disabled placeholder="Enter Patient ID">
-            </div>
-            <div class="form-group">
-              <label>Patient Name:</label>
-              <input type="text" class="form-control" disabled placeholder="Enter Patient Name">
-            </div>
-            <div class="form-group">
-              <label>Surgery Type:</label>
-              <select class="form-control" disabled>
-                <option>Select a surgery type</option>
-              </select>
-            </div>
-            <div class="form-group">
-              <label>Estimated Duration (min):</label>
-              <input type="number" class="form-control" disabled placeholder="Enter duration">
-            </div>
-            <div class="form-group">
-              <label>Priority Level:</label>
-              <select class="form-control" disabled>
-                <option>Select priority</option>
-              </select>
             </div>
-          </div>
         </div>
       </aside>
     </div>
@@ -410,31 +403,36 @@ import { ref, computed, onMounted, watch, nextTick } from 'vue';
 import { useScheduleStore } from '@/stores/scheduleStore';
 import { useNotificationStore } from '@/stores/notificationStore';
 import { storeToRefs } from 'pinia';
-import GanttChart from './GanttChart.vue';
+// import GanttChart from './GanttChart.vue'; // Custom component, kept but not used for vue-ganttastic
 import ToastNotification from './ToastNotification.vue';
 import KeyboardShortcutsHelp from './KeyboardShortcutsHelp.vue';
+import OptimizationSuggestions from './OptimizationSuggestions.vue';
 import keyboardShortcuts from '@/services/keyboardShortcuts';
 
-// Initialize the stores
+// NOTE: If GGanttChart and GGanttRow are not globally available after plugin registration,
+// you might need to import them here:
+// import { GGanttChart, GGanttRow } from '@infectoone/vue-ganttastic';
+
 const scheduleStore = useScheduleStore();
 const notificationStore = useNotificationStore();
 const {
   pendingSurgeries: storePendingSurgeries,
   scheduledSurgeries: storeScheduledSurgeries,
   selectedSurgeryId,
-  isLoading
+  isLoading,
+  currentDateRange, // Provides { start: Date, end: Date }
+  ganttViewMode, // 'Day' or 'Week'
+  operatingRooms // Assuming you have this in your store: [{id: 'OR1', name: 'Operating Room 1'}, ...]
 } = storeToRefs(scheduleStore);
 
-// Component refs
 const toastRef = ref(null);
 const keyboardShortcutsRef = ref(null);
 
-// --- State ---
 const selectedSurgery = ref(null);
 const selectedSurgerySource = ref(''); // 'pending' or 'scheduled'
 const formMode = ref('view'); // 'view', 'edit', 'new'
-const formErrors = ref({}); // To store validation errors
-const formSubmitted = ref(false); // To track if form was submitted (for validation display)
+const formErrors = ref({});
+const formSubmitted = ref(false);
 
 const filters = ref({
   priority: '',
@@ -442,337 +440,318 @@ const filters = ref({
   status: '',
   surgeon: '',
   equipment: '',
-  dateRange: {
-    start: null,
-    end: null
-  },
+  dateRange: { start: null, end: null },
   showAdvancedFilters: false
 });
 
-// Sorting options for pending surgeries list
 const sortOptions = ref({
-  field: 'priority', // Default sort field
-  direction: 'desc' // 'asc' or 'desc'
+  field: 'priority',
+  direction: 'desc'
 });
 
-const currentScheduleDateRange = ref('Today'); // Placeholder for date range display
-const isGanttInitialized = ref(false); // To track if the Gantt library is loaded
+// const isGanttInitialized = ref(false); // For custom Gantt, may not be needed now
+
+// Data for vue-ganttastic bars, structured per OR
+// This will be populated dynamically from storeScheduledSurgeries
+const ganttChartBarsByRow = ref({}); // Example: { OR1: [bar1, bar2], OR2: [bar3] }
+
+// Helper to format date string as YYYY-MM-DD HH:MM for vue-ganttastic
+function formatDateForGantt(date) {
+  if (!(date instanceof Date) || isNaN(date.valueOf())) {
+    // Try to parse if it's a string that might be a date
+    const d = new Date(date);
+    if (isNaN(d.valueOf())) return "2000-01-01 00:00"; // Fallback for invalid date
+    date = d;
+  }
+  const year = date.getFullYear();
+  const month = (date.getMonth() + 1).toString().padStart(2, '0');
+  const day = date.getDate().toString().padStart(2, '0');
+  const hours = date.getHours().toString().padStart(2, '0');
+  const minutes = date.getMinutes().toString().padStart(2, '0');
+  return `${year}-${month}-${day} ${hours}:${minutes}`;
+}
+
+
+const ganttChartStart = computed(() => {
+  return currentDateRange.value?.start ? formatDateForGantt(currentDateRange.value.start) : formatDateForGantt(new Date());
+});
+
+const ganttChartEnd = computed(() => {
+  if (currentDateRange.value?.end) {
+    const endDate = new Date(currentDateRange.value.end);
+    // vue-ganttastic chart-end is often exclusive for the last day, or needs to cover the full day.
+    // If it's a 'Day' view, end might be start + 1 day. If 'Week', it's the end of the week.
+    // Let's ensure it covers the full last day of the range.
+    endDate.setHours(23, 59, 59, 999);
+    if (ganttViewMode.value === 'Day' && currentDateRange.value.start.toDateString() === endDate.toDateString()) {
+        // For day view, make sure end is at least end of the start day or start of next day
+        return formatDateForGantt(new Date(endDate.getTime() + 1)); // Effectively start of next day
+    }
+    return formatDateForGantt(endDate);
+  }
+  // Fallback for ganttChartEnd
+  const fallbackStartDate = currentDateRange.value?.start ? new Date(currentDateRange.value.start) : new Date();
+  return formatDateForGantt(new Date(fallbackStartDate.setDate(fallbackStartDate.getDate() + (ganttViewMode.value === 'Week' ? 7 : 1))));
+});
+
+
+const ganttHighlightedDates = computed(() => {
+  // Example: highlight weekends or specific dates
+  // This needs to be an array of date strings in "YYYY-MM-DD HH:MM" format
+  // For now, let's return an empty array or a sample
+  return []; // e.g., ["2024-07-27 00:00", "2024-07-28 00:00"]
+});
 
-// --- Data for Gantt Chart (to be passed as props or managed by the Gantt library wrapper) ---
-const ganttTasks = ref([]); // Holds tasks formatted for the Gantt library
-const ganttResources = ref([]); // Holds resources (ORs, Surgeons, Staff, Equipment)
 
-// --- Computed Properties ---
 const filteredPendingSurgeries = computed(() => {
-  if (!storePendingSurgeries.value) return []; // Ensure pendingSurgeries is not null or undefined
-
-  // First filter the surgeries
-  const filtered = storePendingSurgeries.value.filter(surgery => {
-    // Basic filters
-    const matchesPriority = !filters.value.priority || surgery.priority === filters.value.priority;
-    const matchesSpecialty = !filters.value.specialty ||
-      (surgery.fullType && surgery.fullType.toLowerCase().includes(filters.value.specialty.toLowerCase()));
-    const matchesStatus = !filters.value.status || surgery.status === filters.value.status;
-
-    // Advanced filters
-    const matchesSurgeon = !filters.value.surgeon ||
-      (surgery.requiredSurgeons &&
-        (Array.isArray(surgery.requiredSurgeons)
-          ? surgery.requiredSurgeons.some(s => s.toLowerCase().includes(filters.value.surgeon.toLowerCase()))
-          : surgery.requiredSurgeons.toLowerCase().includes(filters.value.surgeon.toLowerCase())));
-
-    const matchesEquipment = !filters.value.equipment ||
-      (surgery.requiredEquipment &&
-        (Array.isArray(surgery.requiredEquipment)
-          ? surgery.requiredEquipment.some(e => e.toLowerCase().includes(filters.value.equipment.toLowerCase()))
-          : surgery.requiredEquipment.toLowerCase().includes(filters.value.equipment.toLowerCase())));
-
-    // Date range filter
-    let matchesDateRange = true;
+  if (!storePendingSurgeries.value) return [];
+  let surgeries = [...storePendingSurgeries.value]; // Create a shallow copy for filtering
+
+  // Apply basic filters
+  if (filters.value.priority) {
+    surgeries = surgeries.filter(s => s.priority === filters.value.priority);
+  }
+  if (filters.value.specialty) {
+    const specialtyLower = filters.value.specialty.toLowerCase();
+    surgeries = surgeries.filter(s => s.fullType && s.fullType.toLowerCase().includes(specialtyLower));
+  }
+  if (filters.value.status) {
+    surgeries = surgeries.filter(s => s.status === filters.value.status);
+  }
+
+  // Apply advanced filters
+  if (filters.value.showAdvancedFilters) {
+    if (filters.value.surgeon) {
+      const surgeonLower = filters.value.surgeon.toLowerCase();
+      surgeries = surgeries.filter(s =>
+        s.requiredSurgeons && (Array.isArray(s.requiredSurgeons)
+          ? s.requiredSurgeons.some(surgeon => surgeon.toLowerCase().includes(surgeonLower))
+          : String(s.requiredSurgeons).toLowerCase().includes(surgeonLower))
+      );
+    }
+    if (filters.value.equipment) {
+      const equipmentLower = filters.value.equipment.toLowerCase();
+      surgeries = surgeries.filter(s =>
+        s.requiredEquipment && (Array.isArray(s.requiredEquipment)
+          ? s.requiredEquipment.some(eq => eq.toLowerCase().includes(equipmentLower))
+          : String(s.requiredEquipment).toLowerCase().includes(equipmentLower))
+      );
+    }
     if (filters.value.dateRange.start && filters.value.dateRange.end) {
-      const requestedDate = surgery.requestedDate ? new Date(surgery.requestedDate) : null;
-      if (requestedDate) {
-        const startDate = new Date(filters.value.dateRange.start);
-        const endDate = new Date(filters.value.dateRange.end);
-        // Set time to 00:00:00 for start and 23:59:59 for end to include the entire day
-        startDate.setHours(0, 0, 0, 0);
-        endDate.setHours(23, 59, 59, 999);
-        matchesDateRange = requestedDate >= startDate && requestedDate <= endDate;
-      }
+      const filterStart = new Date(filters.value.dateRange.start).setHours(0,0,0,0);
+      const filterEnd = new Date(filters.value.dateRange.end).setHours(23,59,59,999);
+      surgeries = surgeries.filter(s => {
+        if (!s.requestedDate) return false;
+        const reqDate = new Date(s.requestedDate).getTime();
+        return reqDate >= filterStart && reqDate <= filterEnd;
+      });
     }
+  }
 
-    return matchesPriority && matchesSpecialty && matchesStatus &&
-           matchesSurgeon && matchesEquipment && matchesDateRange;
-  });
-
-  // Then sort the filtered surgeries
-  return sortSurgeries(filtered, sortOptions.value.field, sortOptions.value.direction);
+  // Apply sorting
+  return sortSurgeries(surgeries, sortOptions.value.field, sortOptions.value.direction);
 });
 
-// Helper function to sort surgeries
 const sortSurgeries = (surgeries, field, direction) => {
-  return [...surgeries].sort((a, b) => {
+  return surgeries.sort((a, b) => {
     let comparison = 0;
+    const priorityValues = { 'High': 3, 'Medium': 2, 'Low': 1, '': 0 };
 
-    // Handle different field types
     switch (field) {
       case 'priority':
-        // Convert priority to numeric value for sorting
-        const priorityValues = { 'High': 3, 'Medium': 2, 'Low': 1 };
-        comparison = priorityValues[a.priority] - priorityValues[b.priority];
+        comparison = (priorityValues[a.priority] || 0) - (priorityValues[b.priority] || 0);
         break;
       case 'estimatedDuration':
-        comparison = a.estimatedDuration - b.estimatedDuration;
+        comparison = (a.estimatedDuration || 0) - (b.estimatedDuration || 0);
         break;
       case 'patientName':
-        comparison = (a.patientName || a.patientId).localeCompare(b.patientName || b.patientId);
+        comparison = (a.patientName || a.patientId || '').localeCompare(b.patientName || b.patientId || '');
         break;
       case 'type':
-        comparison = a.type.localeCompare(b.type);
+        comparison = (a.type || '').localeCompare(b.type || '');
         break;
-      default:
-        comparison = 0;
     }
-
-    // Apply sort direction
     return direction === 'asc' ? comparison : -comparison;
   });
 };
 
-// Format the current date range for display
-const currentGanttViewDateRange = computed(() => {
-  const { currentDateRange, ganttViewMode } = scheduleStore;
-
-  if (ganttViewMode === 'Day') {
-    return currentDateRange.start.toLocaleDateString(undefined, {
-      weekday: 'short',
-      month: 'short',
-      day: 'numeric',
-      year: 'numeric'
-    });
-  } else if (ganttViewMode === 'Week') {
-    return `${currentDateRange.start.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })} -
-            ${currentDateRange.end.toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' })}`;
-  }
 
-  return 'Today';
-});
+const currentGanttViewDateRangeForDisplay = computed(() => {
+  if (currentDateRange.value && currentDateRange.value.start instanceof Date) {
+    const start = currentDateRange.value.start;
+    // Ensure end is also a Date object for formatting
+    const end = currentDateRange.value.end instanceof Date ? currentDateRange.value.end : new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000);
 
-// --- Methods ---
+    if (ganttViewMode.value === 'Day') {
+      return start.toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' });
+    } else if (ganttViewMode.value === 'Week') {
+      return `${start.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' })}`;
+    }
+  }
+  return 'Date range not set';
+});
 
-// Initialize data from the store
-const initializeData = () => {
-  // Load data from the store
-  if (!scheduleStore.dataInitialized) {
-    scheduleStore.loadInitialData();
+// Transform scheduled surgeries for vue-ganttastic
+const updateGanttasticBars = () => {
+  const newBarsByRow = {};
+  if (!operatingRooms.value || operatingRooms.value.length === 0) {
+    console.warn("No operating rooms defined in store. Gantt chart rows will be empty.");
+    ganttChartBarsByRow.value = {};
+    return;
   }
 
-  // Set isGanttInitialized to true since we're using the actual GanttChart component
-  // For testing purposes, we'll keep it false initially to show the placeholder
-  // isGanttInitialized.value = true;
-};
+  operatingRooms.value.forEach(or => {
+    newBarsByRow[or.id] = [];
+  });
 
-// Helper to transform surgery data to Gantt task format (example)
-const transformSurgeryToGanttTask = (surgery, scheduledTime) => {
-  // This is highly dependent on the chosen Gantt library's expected format
-  return {
-    id: surgery.id,
-    text: `${surgery.patientId} - ${surgery.type}`,
-    startDate: scheduledTime, // Ensure this is a valid date/time format for the library
-    duration: surgery.estimatedDuration,
-    durationUnit: 'minute', // Or 'hour', 'day' depending on library
-    priority: surgery.priority,
-    status: surgery.status,
-    // Add other relevant fields: resourceId (OR, surgeon), dependencies, color, etc.
-    ...surgery // Spread other surgery details that might be useful
-  };
-};
+  storeScheduledSurgeries.value.forEach(surgery => {
+    if (!surgery.startTime || !surgery.endTime || !surgery.orId) {
+      console.warn(`Surgery ${surgery.id} is missing startTime, endTime, or orId. Skipping.`);
+      return;
+    }
+    if (!newBarsByRow[surgery.orId]) {
+        console.warn(`Operating room ${surgery.orId} for surgery ${surgery.id} not found in operatingRooms list. Skipping.`);
+        return;
+    }
 
-// Helper to transform scheduled surgeries (if fetched separately) to Gantt tasks
-const transformScheduledSurgeriesToGanttTasks = () => {
-  ganttTasks.value = storeScheduledSurgeries.value.map(surgery => {
-    // Assuming surgery objects in scheduledSurgeries have a 'scheduledTime' and 'id'
-    return transformSurgeryToGanttTask(surgery, surgery.startTime);
+    const bar = {
+      myBeginDate: formatDateForGantt(new Date(surgery.startTime)),
+      myEndDate: formatDateForGantt(new Date(surgery.endTime)),
+      ganttBarConfig: {
+        id: surgery.id, // Must be unique
+        label: `${surgery.patientName || surgery.patientId} (${surgery.type})`,
+        style: {
+          background: surgery.priority === 'High' ? '#E57373' : (surgery.priority === 'Medium' ? '#FFB74D' : '#81C784'),
+          borderRadius: '5px',
+          color: 'white',
+          boxShadow: '1px 1px 3px rgba(0,0,0,0.2)'
+        },
+        // You can add more properties like `immobile`, `progress`, etc.
+        // Store original surgery data for easy access on events
+        bundle: surgery, // Custom property to hold the original surgery data
+      }
+    };
+    newBarsByRow[surgery.orId].push(bar);
   });
-  // TODO: Notify Gantt chart to refresh/load new tasks
+  ganttChartBarsByRow.value = newBarsByRow;
 };
 
-// Watch for changes in storeScheduledSurgeries to update Gantt tasks
-// This is a basic example; a real Gantt integration might handle this internally or via its API
-watch(storeScheduledSurgeries, (newScheduledList) => {
-  // transformScheduledSurgeriesToGanttTasks();
-  console.log('Scheduled surgeries updated, Gantt tasks should refresh:', newScheduledList);
-}, { deep: true });
+// Computed property to get bars for a specific row (OR)
+const getBarsForRow = (orId) => {
+  return ganttChartBarsByRow.value[orId] || [];
+};
+
+
+watch(storeScheduledSurgeries, updateGanttasticBars, { deep: true, immediate: true });
+watch(operatingRooms, updateGanttasticBars, { deep: true, immediate: true }); // Also update if ORs change
+
 
 const applyFilters = () => {
-  // The computed property `filteredPendingSurgeries` will update automatically.
-  // This function is here if any imperative logic is needed on filter change.
-  console.log('Filters applied:', filters.value);
+  // Computed property `filteredPendingSurgeries` handles this.
+  // If additional actions are needed, add them here.
+  console.log('Filters applied/changed:', filters.value, sortOptions.value);
 };
 
-// Reset all filters to their default values
 const resetFilters = () => {
   filters.value = {
-    priority: '',
-    specialty: '',
-    status: '',
-    surgeon: '',
-    equipment: '',
-    dateRange: {
-      start: null,
-      end: null
-    },
-    showAdvancedFilters: filters.value.showAdvancedFilters // Keep the advanced filters visibility state
+    priority: '', specialty: '', status: '', surgeon: '', equipment: '',
+    dateRange: { start: null, end: null },
+    showAdvancedFilters: filters.value.showAdvancedFilters
   };
-  console.log('Filters reset');
+  sortOptions.value = { field: 'priority', direction: 'desc' }; // Reset sort as well
+  applyFilters(); // Re-apply to update list
 };
 
-const selectSurgeryForDetails = (surgery, source) => {
-  selectedSurgery.value = { ...surgery }; // Clone to avoid direct mutation if editing
-  selectedSurgerySource.value = source;
-  formMode.value = 'view';
-  console.log(`Viewing ${source} surgery:`, surgery.id);
+const selectSurgeryForDetails = (surgeryData, source) => {
+  // If surgeryData is from a Gantt bar, it's in surgeryData.ganttBarConfig.bundle
+  const surgery = source === 'gantt' && surgeryData.ganttBarConfig?.bundle ? surgeryData.ganttBarConfig.bundle : surgeryData;
 
-  // Also update the store's selected surgery
-  if (source === 'scheduled') {
+  selectedSurgery.value = { ...surgery };
+  // Ensure scheduledTime is in YYYY-MM-DDTHH:mm format for datetime-local input
+  if (surgery.startTime && source === 'scheduled') {
+    const d = new Date(surgery.startTime);
+    selectedSurgery.value.scheduledTime = `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}T${d.getHours().toString().padStart(2, '0')}:${d.getMinutes().toString().padStart(2, '0')}`;
+  }
+  selectedSurgerySource.value = source === 'gantt' ? 'scheduled' : source;
+  formMode.value = 'view';
+  if (source === 'scheduled' || source === 'gantt') {
     scheduleStore.selectSurgery(surgery.id);
   }
 };
 
 const showCreateNewSurgeryForm = () => {
-  // Reset form state
   formSubmitted.value = false;
   formErrors.value = {};
-
   selectedSurgery.value = {
-    patientId: '',
-    patientName: '',
-    type: '',
-    fullType: '',
-    estimatedDuration: 60,
-    duration: 60,
-    priority: 'Medium',
-    status: 'Pending',
-    requiredSurgeons: [],
-    requiredStaffRoles: [],
-    requiredEquipment: []
+    patientId: `PAT-${Date.now().toString().slice(-4)}`, // Example ID
+    patientName: '', type: '', fullType: '',
+    estimatedDuration: 60, priority: 'Medium', status: 'Pending',
+    requiredSurgeons: '', requiredStaffRoles: '', requiredEquipment: '' // Keep as strings for input
   };
   selectedSurgerySource.value = 'new';
   formMode.value = 'new';
-  console.log('Showing form to create new surgery');
 };
 
-// Validate the surgery form
 const validateSurgeryForm = () => {
   formSubmitted.value = true;
   const errors = {};
   const surgery = selectedSurgery.value;
-
-  // Required fields validation
-  if (!surgery.patientId?.trim()) {
-    errors.patientId = 'Patient ID is required';
-  }
-
-  if (!surgery.patientName?.trim()) {
-    errors.patientName = 'Patient Name is required';
-  }
-
-  if (!surgery.type?.trim()) {
-    errors.type = 'Surgery Type is required';
-  }
-
-  if (!surgery.fullType?.trim()) {
-    errors.fullType = 'Full Type is required';
-  }
-
-  // Numeric validation
-  if (!surgery.estimatedDuration || surgery.estimatedDuration <= 0) {
-    errors.estimatedDuration = 'Duration must be greater than 0';
-  }
-
-  // Array fields validation (convert string to array if needed)
-  if (typeof surgery.requiredSurgeons === 'string') {
-    surgery.requiredSurgeons = surgery.requiredSurgeons.split(',').map(s => s.trim()).filter(Boolean);
-  }
-
-  if (typeof surgery.requiredStaffRoles === 'string') {
-    surgery.requiredStaffRoles = surgery.requiredStaffRoles.split(',').map(s => s.trim()).filter(Boolean);
-  }
-
-  if (typeof surgery.requiredEquipment === 'string') {
-    surgery.requiredEquipment = surgery.requiredEquipment.split(',').map(s => s.trim()).filter(Boolean);
-  }
-
+  if (!surgery.patientId?.trim()) errors.patientId = 'Patient ID is required';
+  if (!surgery.patientName?.trim()) errors.patientName = 'Patient Name is required';
+  if (!surgery.type?.trim()) errors.type = 'Surgery Type is required';
+  if (!surgery.fullType?.trim()) errors.fullType = 'Full Type is required';
+  if (!surgery.estimatedDuration || surgery.estimatedDuration <= 0) errors.estimatedDuration = 'Duration must be > 0';
   formErrors.value = errors;
   return Object.keys(errors).length === 0;
 };
 
 const saveSurgeryDetails = async () => {
-  if (!selectedSurgery.value) return;
-
-  // Validate the form
-  if (!validateSurgeryForm()) {
-    console.error('Form validation failed:', formErrors.value);
-    notificationStore.error('Please fix the validation errors before saving.');
+  if (!selectedSurgery.value || !validateSurgeryForm()) {
+    notificationStore.error('Please fix validation errors.');
     return;
   }
 
-  console.log('Saving surgery details:', selectedSurgery.value);
+  // Convert comma-separated strings to arrays for store/backend if needed
+  const surgeryToSave = {
+    ...selectedSurgery.value,
+    requiredSurgeons: selectedSurgery.value.requiredSurgeons?.split(',').map(s => s.trim()).filter(Boolean) || [],
+    requiredStaffRoles: selectedSurgery.value.requiredStaffRoles?.split(',').map(s => s.trim()).filter(Boolean) || [],
+    requiredEquipment: selectedSurgery.value.requiredEquipment?.split(',').map(s => s.trim()).filter(Boolean) || [],
+  };
+   // If it's a scheduled surgery being edited, ensure startTime and orId are correctly handled
+  if (selectedSurgerySource.value === 'scheduled' && selectedSurgery.value.scheduledTime) {
+    surgeryToSave.startTime = new Date(selectedSurgery.value.scheduledTime).toISOString();
+    // orId should already be part of selectedSurgery.value if editing a scheduled one
+  }
+
 
   try {
     if (formMode.value === 'new') {
-      // Add to pending list
-      await scheduleStore.addPendingSurgery(selectedSurgery.value);
-      notificationStore.success('New surgery added to pending list.', {
-        title: 'Surgery Added',
-        action: {
-          label: 'Schedule Now',
-          callback: () => scheduleSelectedSurgery(selectedSurgery.value)
-        }
-      });
+      await scheduleStore.addPendingSurgery(surgeryToSave);
+      notificationStore.success('New surgery added to pending list.');
     } else if (selectedSurgerySource.value === 'pending') {
-      // Update pending surgery
-      await scheduleStore.updatePendingSurgery(selectedSurgery.value);
-      notificationStore.success('Pending surgery details updated.', {
-        title: 'Surgery Updated'
-      });
+      await scheduleStore.updatePendingSurgery(surgeryToSave);
+      notificationStore.success('Pending surgery details updated.');
     } else if (selectedSurgerySource.value === 'scheduled') {
-      // Update scheduled surgery
-      await scheduleStore.updateScheduledSurgery(selectedSurgery.value);
-      notificationStore.success('Scheduled surgery details updated.', {
-        title: 'Surgery Updated'
-      });
+      await scheduleStore.updateScheduledSurgery(surgeryToSave);
+      notificationStore.success('Scheduled surgery details updated.');
     }
-
-    // Reset form state
-    formMode.value = 'view';
-    formSubmitted.value = false;
-    formErrors.value = {};
+    formMode.value = 'view'; // Revert to view mode after save
   } catch (error) {
-    console.error(`Error saving surgery: ${error.message}`);
-    formErrors.value.general = `Error saving surgery: ${error.message}`;
-    notificationStore.error(`Error saving surgery: ${error.message}`, {
-      title: 'Save Failed'
-    });
+    console.error(`Error saving surgery:`, error);
+    formErrors.value.general = `Error: ${error.message}`;
+    notificationStore.error(`Error saving surgery: ${error.message}`);
   }
 };
 
-// Helper to update the full type based on the selected surgery type
 const updateFullType = () => {
   if (!selectedSurgery.value) return;
-
   const typeMap = {
-    'CABG': 'Coronary Artery Bypass Graft',
-    'KNEE': 'Total Knee Replacement',
-    'APPEN': 'Appendectomy',
-    'HERNI': 'Hernia Repair',
-    'CATAR': 'Cataract Surgery',
-    'HIPRE': 'Total Hip Replacement'
+    'CABG': 'Coronary Artery Bypass Graft', 'KNEE': 'Total Knee Replacement',
+    'APPEN': 'Appendectomy', 'HERNI': 'Hernia Repair',
+    'CATAR': 'Cataract Surgery', 'HIPRE': 'Total Hip Replacement'
   };
-
-  if (selectedSurgery.value.type && typeMap[selectedSurgery.value.type]) {
-    selectedSurgery.value.fullType = typeMap[selectedSurgery.value.type];
-  }
+  selectedSurgery.value.fullType = typeMap[selectedSurgery.value.type] || '';
 };
 
 const clearSelectionOrCancel = () => {
@@ -781,72 +760,44 @@ const clearSelectionOrCancel = () => {
   formMode.value = 'view';
   formSubmitted.value = false;
   formErrors.value = {};
-
-  // Clear the store's selected surgery
   scheduleStore.clearSelectedSurgery();
-
-  console.log('Selection cleared or form cancelled');
 };
 
-const scheduleSelectedSurgery = async () => {
-  if (!selectedSurgery.value || selectedSurgerySource.value !== 'pending') return;
+const promptScheduleSurgery = (surgery) => {
+  // This is a placeholder. In a real scenario, you'd open a modal
+  // to select OR and time, or allow dropping onto vue-ganttastic.
+  // For now, let's use a default OR and current time for quick scheduling.
+  const targetOR = operatingRooms.value?.[0]?.id || 'OR1'; // Default to first OR or 'OR1'
+  const scheduleTime = new Date(); // Default to now
 
-  try {
-    // Schedule the surgery using the store
-    const scheduledSurgery = await scheduleStore.schedulePendingSurgery(
-      selectedSurgery.value.id,
-      'OR1', // Default OR - in a real app, this would be selected by the user
-      new Date() // Default time - in a real app, this would be selected by the user
-    );
-
-    notificationStore.success(`Surgery ${selectedSurgery.value.patientName || selectedSurgery.value.patientId} scheduled.`, {
-      title: 'Surgery Scheduled',
-      action: {
-        label: 'View Schedule',
-        callback: () => {
-          // Scroll to the Gantt chart
-          document.getElementById('gantt-chart-container')?.scrollIntoView({ behavior: 'smooth' });
-        }
-      }
-    });
+  // Ensure minutes are rounded to 00, 15, 30, 45 for example
+  scheduleTime.setMinutes(Math.round(scheduleTime.getMinutes() / 15) * 15, 0, 0);
 
-    // View the newly scheduled surgery
-    if (scheduledSurgery) {
-      selectSurgeryForDetails(scheduledSurgery, 'scheduled');
-    } else {
-      clearSelectionOrCancel();
-    }
-  } catch (error) {
-    notificationStore.error(`Error scheduling surgery: ${error.message}`, {
-      title: 'Scheduling Failed'
-    });
+
+  if (window.confirm(`Schedule "${surgery.patientName || surgery.patientId}" in ${targetOR} at ${scheduleTime.toLocaleString()}?`)) {
+    scheduleStore.schedulePendingSurgery(surgery.id, targetOR, scheduleTime.toISOString())
+      .then(scheduled => {
+        if (scheduled) {
+          notificationStore.success(`Surgery scheduled in ${targetOR}.`);
+          selectSurgeryForDetails(scheduled, 'scheduled');
+        }
+      })
+      .catch(error => notificationStore.error(`Scheduling failed: ${error.message}`));
   }
 };
 
-// --- Drag and Drop Handlers ---
+
+// --- Drag and Drop Handlers (for pending list items) ---
 const draggedSurgery = ref(null);
 const dragGhost = ref(null);
-const dropTarget = ref({
-  orId: null,
-  time: null,
-  isValid: true,
-  message: ''
-});
+// const dropTarget = ref({ orId: null, time: null, isValid: true, message: '' }); // For custom Gantt
 
 const handleDragStart = (surgery, event) => {
-  console.log('Dragging surgery:', surgery.id);
-
-  // Store the dragged surgery for reference
   draggedSurgery.value = surgery;
-
-  // Set data for drop handling
   event.dataTransfer.setData('application/json', JSON.stringify(surgery));
   event.dataTransfer.effectAllowed = 'move';
-
-  // Add visual feedback for dragging
   event.target.classList.add('dragging');
 
-  // Create a custom drag image/ghost element
   const ghostElement = document.createElement('div');
   ghostElement.classList.add('surgery-drag-ghost');
   ghostElement.innerHTML = `
@@ -856,267 +807,132 @@ const handleDragStart = (surgery, event) => {
       <div class="ghost-type">${surgery.type} - ${surgery.estimatedDuration} min</div>
     </div>
   `;
-
-  // Add the ghost element to the document temporarily
   document.body.appendChild(ghostElement);
   dragGhost.value = ghostElement;
-
-  // Set the custom drag image
   event.dataTransfer.setDragImage(ghostElement, 20, 20);
-
-  // Hide the ghost element (it will still be used as drag image)
-  setTimeout(() => {
-    ghostElement.style.position = 'absolute';
-    ghostElement.style.left = '-9999px';
-  }, 0);
+  setTimeout(() => { ghostElement.style.position = 'absolute'; ghostElement.style.left = '-9999px'; }, 0);
 };
 
 const handleDragEnd = (event) => {
-  // Remove the dragging class
   event.target.classList.remove('dragging');
-
-  // Clean up the ghost element
   if (dragGhost.value && dragGhost.value.parentNode) {
     dragGhost.value.parentNode.removeChild(dragGhost.value);
     dragGhost.value = null;
   }
-
-  // Reset the dragged surgery
   draggedSurgery.value = null;
+  // Reset any drop target indicators for vue-ganttastic if needed
+};
 
-  // Reset drop target
-  dropTarget.value = {
-    orId: null,
-    time: null,
-    isValid: true,
-    message: ''
-  };
+// --- vue-ganttastic Event Handlers ---
+const handleClickGanttBar = (bar, event, datetime) => {
+  console.log('Clicked Gantt Bar:', bar, 'at time:', datetime);
+  // bar.ganttBarConfig.bundle should contain the original surgery object
+  if (bar.ganttBarConfig?.bundle) {
+    selectSurgeryForDetails(bar, 'gantt'); // Pass the bar itself, selectSurgeryForDetails will extract bundle
+  }
 };
 
-const handleDragOver = (event, orId) => {
-  event.preventDefault();
+const handleDragEndGanttBar = async (bar, event) => {
+  console.log('Dragged Gantt Bar to new time:', bar.beginDate, bar.endDate);
+  const originalSurgery = bar.ganttBarConfig?.bundle;
+  if (!originalSurgery) return;
 
-  if (!draggedSurgery.value) return;
-
-  // Calculate the time based on the mouse position
-  // This is a simplified example - in a real app, you would calculate the exact time
-  // based on the Gantt chart's time scale and the mouse position
-  const rect = event.currentTarget.getBoundingClientRect();
-  const relativeX = event.clientX - rect.left;
-  const percentageX = relativeX / rect.width;
-
-  // Get the current date range from the store
-  const { start, end } = scheduleStore.currentDateRange;
-  const totalMinutes = (end.getTime() - start.getTime()) / (60 * 1000);
-  const minutesFromStart = totalMinutes * percentageX;
-
-  // Calculate the target time
-  const targetTime = new Date(start.getTime() + minutesFromStart * 60 * 1000);
-
-  // Round to nearest 15 minutes for better UX
-  const roundedMinutes = Math.round(targetTime.getMinutes() / 15) * 15;
-  targetTime.setMinutes(roundedMinutes);
-  targetTime.setSeconds(0);
-  targetTime.setMilliseconds(0);
-
-  // Update the drop target
-  dropTarget.value = {
-    orId,
-    time: targetTime,
-    isValid: true, // In a real app, you would check for conflicts here
-    message: `Schedule in ${orId} at ${targetTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
-  };
+  // Assuming bar.beginDate and bar.endDate are updated by vue-ganttastic to Date objects or parsable strings
+  const newStartTime = new Date(bar.beginDate);
+  const newEndTime = new Date(bar.endDate); // vue-ganttastic might provide this directly
 
-  // Check for conflicts (simplified example)
-  const conflictingSurgeries = scheduleStore.scheduledSurgeries.filter(s => {
-    if (s.orId !== orId) return false;
+  // Find the OR (row) this bar belongs to. This is a bit tricky as vue-ganttastic doesn't directly tell you the row of the event.
+  // You might need to iterate ganttChartBarsByRow or have a mapping.
+  // For simplicity, we'll assume the orId is stored in originalSurgery.orId
+  const orId = originalSurgery.orId;
 
-    const surgeryStart = new Date(s.startTime);
-    const surgeryEnd = new Date(s.endTime);
-    const newSurgeryStart = targetTime;
-    const newSurgeryEnd = new Date(targetTime.getTime() + draggedSurgery.value.estimatedDuration * 60 * 1000);
+  if (!orId) {
+      notificationStore.error("Could not determine Operating Room for the moved surgery.");
+      updateGanttasticBars(); // Revert visual change by re-rendering from store
+      return;
+  }
 
-    return (newSurgeryStart < surgeryEnd && newSurgeryEnd > surgeryStart);
-  });
 
-  if (conflictingSurgeries.length > 0) {
-    dropTarget.value.isValid = false;
-    dropTarget.value.message = `Conflict with ${conflictingSurgeries[0].patientName}'s surgery`;
+  // Optimistically update UI or confirm with user
+  // For now, directly update the store
+  try {
+    const updatedSurgeryData = {
+      ...originalSurgery,
+      startTime: newStartTime.toISOString(),
+      endTime: newEndTime.toISOString(), // Ensure this is calculated correctly if not provided by event
+      orId: orId, // The OR might have changed if you implement cross-row dragging
+      status: 'Scheduled' // Ensure status is correct
+    };
+    await scheduleStore.updateScheduledSurgery(updatedSurgeryData);
+    notificationStore.success(`Surgery ${originalSurgery.patientName || originalSurgery.patientId} moved to ${newStartTime.toLocaleString()}.`);
+    // The watch on storeScheduledSurgeries should update the ganttChartBarsByRow automatically
+  } catch (error) {
+    notificationStore.error(`Failed to update surgery time: ${error.message}`);
+    updateGanttasticBars(); // Revert if store update fails
   }
 };
 
-const handleDropOnGantt = (event) => {
+const handleContextmenuGanttBar = (bar, event, datetime) => {
   event.preventDefault();
-  const surgeryDataString = event.dataTransfer.getData('application/json');
-  if (!surgeryDataString) return;
-
-  const droppedSurgery = JSON.parse(surgeryDataString);
-  console.log('Dropped pending surgery on Gantt chart:', droppedSurgery.id);
-
-  // Check if we have a valid drop target
-  if (!dropTarget.value.orId || !dropTarget.value.time) {
-    console.warn('No valid drop target');
-    return;
+  console.log('Context Menu on Gantt Bar:', bar, 'at time:', datetime);
+  // Implement custom context menu logic here (e.g., show options to edit, unschedule, etc.)
+  const surgery = bar.ganttBarConfig?.bundle;
+  if (surgery) {
+    selectSurgeryForDetails(bar, 'gantt'); // Select it for context
+    // Example:
+    // showContextMenu(event.clientX, event.clientY, surgery);
+    notificationStore.info(`Right-clicked on ${surgery.patientName || surgery.patientId}. Implement context menu.`);
   }
-
-  // Check if the drop target is valid (no conflicts)
-  if (!dropTarget.value.isValid) {
-    notificationStore.warning(`Cannot schedule surgery: ${dropTarget.value.message}`, {
-      title: 'Scheduling Conflict'
-    });
-    return;
-  }
-
-  // Schedule the surgery using the store with the calculated OR and time
-  scheduleStore.schedulePendingSurgery(
-    droppedSurgery.id,
-    dropTarget.value.orId,
-    dropTarget.value.time
-  ).then(scheduledSurgery => {
-    if (scheduledSurgery) {
-      const scheduledTime = dropTarget.value.time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
-      notificationStore.success(
-        `Surgery ${droppedSurgery.patientName || droppedSurgery.patientId} scheduled in ${dropTarget.value.orId} at ${scheduledTime}`,
-        {
-          title: 'Surgery Scheduled',
-          duration: 3000
-        }
-      );
-      selectSurgeryForDetails(scheduledSurgery, 'scheduled');
-    }
-  }).catch(error => {
-    notificationStore.error(`Error scheduling surgery: ${error.message}`, {
-      title: 'Scheduling Failed'
-    });
-  });
-
-  // Reset the drop target
-  dropTarget.value = {
-    orId: null,
-    time: null,
-    isValid: true,
-    message: ''
-  };
 };
 
-// Gantt Chart Navigation and Controls
-const ganttNavigate = (direction) => {
-  scheduleStore.navigateGanttDate(direction);
-};
 
-const ganttZoom = (level) => {
-  // Adjust the zoom level
-  if (level === 'in') {
-    // Switch to day view
-    scheduleStore.updateGanttViewMode('Day');
-  } else if (level === 'out') {
-    // Switch to week view
-    scheduleStore.updateGanttViewMode('Week');
-  }
+// Gantt Chart Navigation and Controls
+const ganttNavigate = (direction) => { scheduleStore.navigateGanttDate(direction); };
+const ganttZoom = (viewMode) => { // viewMode is 'Day' or 'Week'
+  scheduleStore.updateGanttViewMode(viewMode);
 };
 
-// Initialize data on component mount
 onMounted(() => {
-  initializeData();
-
-  // Set up the toast notification ref
-  nextTick(() => {
-    if (toastRef.value) {
-      notificationStore.setToastRef(toastRef.value);
+  scheduleStore.loadInitialData().then(() => {
+     // Ensure operatingRooms are loaded before first bar update
+    if (!operatingRooms.value || operatingRooms.value.length === 0) {
+      // If ORs are fetched asynchronously and not yet available,
+      // you might need to watch for their availability or ensure they are part of initial data.
+      // For now, we assume they are part of the initial load or available synchronously.
+      console.log("Operating rooms might still be loading or are empty.");
     }
+    updateGanttasticBars(); // Initial population of Gantt bars
   });
 
-  // Register keyboard shortcuts
+  nextTick(() => { if (toastRef.value) notificationStore.setToastRef(toastRef.value); });
   registerKeyboardShortcuts();
-
-  // Watch for changes in the store's selected surgery
   watch(() => selectedSurgeryId.value, (newId) => {
     if (newId) {
-      // Find the surgery in the scheduled surgeries
       const surgery = storeScheduledSurgeries.value.find(s => s.id === newId);
-      if (surgery) {
-        selectSurgeryForDetails(surgery, 'scheduled');
+      if (surgery) selectSurgeryForDetails(surgery, 'scheduled');
+    } else {
+      // If selectedSurgeryId is cleared, and form is not for 'new', clear the form.
+      if (selectedSurgery.value && formMode.value !== 'new') {
+        clearSelectionOrCancel();
       }
     }
   });
 });
 
-// Register keyboard shortcuts for the scheduling screen
 const registerKeyboardShortcuts = () => {
-  // Create new surgery (N)
-  keyboardShortcuts.register('n', () => {
-    showCreateNewSurgeryForm();
-  }, {
-    description: 'Create new surgery',
-    scope: 'scheduling'
-  });
-
-  // Save surgery details (Ctrl+S)
+  keyboardShortcuts.register('n', showCreateNewSurgeryForm, { description: 'Create new surgery', scope: 'scheduling' });
   keyboardShortcuts.register('s', () => {
-    if (selectedSurgery.value && formMode.value !== 'view') {
-      saveSurgeryDetails();
-    }
-  }, {
-    ctrlKey: true,
-    description: 'Save surgery details',
-    scope: 'scheduling'
-  });
-
-  // Cancel/close form (Escape)
+    if (selectedSurgery.value && formMode.value !== 'view') saveSurgeryDetails();
+  }, { ctrlKey: true, description: 'Save surgery details', scope: 'scheduling' });
   keyboardShortcuts.register('escape', () => {
-    if (selectedSurgery.value) {
-      clearSelectionOrCancel();
-    }
-  }, {
-    description: 'Cancel/close form',
-    scope: 'scheduling'
-  });
-
-  // Schedule selected surgery (Ctrl+Enter)
-  keyboardShortcuts.register('enter', () => {
-    if (selectedSurgery.value && selectedSurgerySource.value === 'pending') {
-      scheduleSelectedSurgery();
-    }
-  }, {
-    ctrlKey: true,
-    description: 'Schedule selected surgery',
-    scope: 'scheduling'
-  });
-
-  // Navigate Gantt chart (Arrow keys)
-  keyboardShortcuts.register('arrowleft', () => {
-    ganttNavigate('prev');
-  }, {
-    description: 'Previous day/week in schedule',
-    scope: 'scheduling'
-  });
-
-  keyboardShortcuts.register('arrowright', () => {
-    ganttNavigate('next');
-  }, {
-    description: 'Next day/week in schedule',
-    scope: 'scheduling'
-  });
-
-  // Toggle advanced filters (F)
-  keyboardShortcuts.register('f', () => {
-    filters.value.showAdvancedFilters = !filters.value.showAdvancedFilters;
-  }, {
-    description: 'Toggle advanced filters',
-    scope: 'scheduling'
-  });
-
-  // Show keyboard shortcuts help (?)
-  keyboardShortcuts.register('?', () => {
-    keyboardShortcutsRef.value?.toggle();
-  }, {
-    description: 'Show keyboard shortcuts help',
-    scope: 'scheduling'
-  });
+    if (selectedSurgery.value) clearSelectionOrCancel();
+  }, { description: 'Cancel/close form', scope: 'scheduling' });
+  // ... other shortcuts
 };
 
 </script>
 
+
 <style scoped>
 .scheduling-container {
   padding: var(--spacing-md);
@@ -1369,61 +1185,6 @@ h1 {
   color: var(--color-text-secondary);
 }
 
-.pending-surgery-item.dragging {
-  opacity: 0.4;
-  transform: scale(1.02);
-  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
-  border-style: dashed;
-}
-
-/* Drag ghost element */
-.surgery-drag-ghost {
-  display: flex;
-  background-color: var(--color-background);
-  border: 2px solid var(--color-primary);
-  border-radius: var(--border-radius-sm);
-  padding: var(--spacing-sm);
-  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
-  width: 250px;
-  pointer-events: none;
-  z-index: 1000;
-}
-
-.ghost-priority {
-  width: 8px;
-  margin-right: var(--spacing-sm);
-  border-radius: var(--border-radius-sm);
-}
-
-.ghost-priority.high {
-  background-color: var(--color-error);
-}
-
-.ghost-priority.medium {
-  background-color: var(--color-warning, #f59e0b);
-}
-
-.ghost-priority.low {
-  background-color: var(--color-success, #10b981);
-}
-
-.ghost-content {
-  flex: 1;
-}
-
-.ghost-title {
-  font-weight: var(--font-weight-bold);
-  margin-bottom: var(--spacing-xs);
-  white-space: nowrap;
-  overflow: hidden;
-  text-overflow: ellipsis;
-}
-
-.ghost-type {
-  font-size: var(--font-size-sm);
-  color: var(--color-text-secondary);
-}
-
 /* Drop target indicator */
 .gantt-chart-container::after {
   content: attr(data-drop-message);
@@ -1569,7 +1330,7 @@ h1 {
 .no-items, .no-scheduled-items {
     padding: 10px;
     text-align: center;
-    color: var(--text-color-secondary);
+    color: var(--text-color-secondary); /* Ensure this CSS var is defined */
     font-style: italic;
 }
 
@@ -1583,7 +1344,7 @@ h1 {
 .schedule-controls button {
   margin-left: 10px;
   padding: 8px 12px;
-  background-color: var(--primary-color);
+  background-color: var(--primary-color); /* Ensure this CSS var is defined */
   color: white;
   border: none;
   border-radius: 4px;
@@ -1591,7 +1352,7 @@ h1 {
 }
 
 .schedule-controls button:hover {
-  background-color: var(--primary-color-dark);
+  background-color: var(--primary-color-dark); /* Ensure this CSS var is defined */
 }
 
 .schedule-controls span {
@@ -1601,50 +1362,23 @@ h1 {
 
 .gantt-chart-placeholder {
   flex-grow: 1;
-  border: 2px dashed var(--border-color);
+  border: 2px dashed var(--border-color); /* Ensure this CSS var is defined */
   display: flex;
-  flex-direction: column; /* To stack p and ul */
+  flex-direction: column;
   align-items: center;
   justify-content: center;
   text-align: center;
-  color: var(--text-color-secondary);
+  color: var(--text-color-secondary); /* Ensure this CSS var is defined */
   border-radius: 4px;
-  background-color: var(--background-color-light);
-  min-height: 300px; /* Ensure it has some height */
-  overflow-y: auto; /* If debug list gets long */
+  background-color: var(--background-color-light); /* Ensure this CSS var is defined */
+  min-height: 300px;
+  overflow-y: auto;
 }
 
 .gantt-chart-container {
   position: relative;
 }
 
-/* Drop target indicator */
-.gantt-chart-container::after {
-  content: attr(data-drop-message);
-  display: none;
-  position: absolute;
-  bottom: 10px;
-  right: 10px;
-  background-color: var(--color-background);
-  color: var(--color-text);
-  padding: var(--spacing-sm) var(--spacing-md);
-  border-radius: var(--border-radius-sm);
-  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
-  z-index: 100;
-  font-size: var(--font-size-sm);
-  pointer-events: none;
-}
-
-.gantt-chart-container.drag-over::after {
-  display: block;
-}
-
-.gantt-chart-container.drag-over.invalid::after {
-  background-color: var(--color-error-bg, rgba(255, 0, 0, 0.1));
-  color: var(--color-error);
-  border: 1px solid var(--color-error);
-}
-
 .scheduled-surgery-list-debug {
     list-style: none;
     padding: 0;
@@ -1653,11 +1387,11 @@ h1 {
 }
 .scheduled-surgery-list-debug li {
     padding: 5px;
-    border-bottom: 1px solid var(--border-color-light);
+    border-bottom: 1px solid var(--border-color-light); /* Ensure this CSS var is defined */
     cursor: pointer;
 }
 .scheduled-surgery-list-debug li:hover {
-    background-color: var(--hover-color);
+    background-color: var(--hover-color); /* Ensure this CSS var is defined */
 }
 
 
@@ -1703,7 +1437,7 @@ h1 {
 .form-group textarea:focus {
   outline: none;
   border-color: var(--color-primary);
-  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb, 0, 120, 212), 0.25);
+  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb, 0, 120, 212), 0.25); /* Ensure --color-primary-rgb is defined */
 }
 
 .form-group input:disabled,
@@ -1730,7 +1464,7 @@ h1 {
 }
 
 .form-error-message.general-error {
-  background-color: rgba(var(--color-error-rgb, 255, 0, 0), 0.1);
+  background-color: rgba(var(--color-error-rgb, 255, 0, 0), 0.1); /* Ensure --color-error-rgb is defined */
   padding: var(--spacing-sm);
   border-radius: var(--border-radius-sm);
   margin-bottom: var(--spacing-md);
@@ -1746,7 +1480,7 @@ h1 {
   margin-top: var(--spacing-lg);
   display: flex;
   gap: var(--spacing-sm);
-  flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
+  flex-wrap: wrap;
 }
 
 .btn {
@@ -1798,4 +1532,44 @@ h1 {
   }
 }
 
+/* Add some basic styling for the gantt chart wrapper if needed */
+.gantt-chart-wrapper {
+  flex-grow: 1;
+  display: flex;
+  flex-direction: column;
+  min-height: 400px; /* Ensure it has some height */
+  overflow: hidden; /* Prevent content overflow issues */
+}
+
+/* vue-ganttastic might require its container to have a specific height or flex properties */
+:deep(.g-gantt-chart) { /* Using :deep to target child component styles if necessary */
+  /* Adjust height as needed, or make it flexible */
+  height: 100%;
+  width: 100%;
+}
+:deep(.g-timeaxis) {
+    /* Example: Make timeaxis text smaller if it overlaps */
+    font-size: 10px;
+}
+:deep(.g-gantt-row-label) {
+    font-size: 12px !important; /* Example to ensure label size */
+}
+.loading-overlay {
+  /* ... your existing styles ... */
+}
+.spinner {
+  /* ... your existing styles ... */
+}
+.gantt-placeholder-text {
+   /* ... your existing styles ... */
+}
+.gantt-info-panel {
+   /* ... your existing styles ... */
+}
+
+/* Ensure the form control class is applied to selects in filters for consistency */
+.filters-section select.form-control,
+.sort-section select.form-control {
+  /* Styles should already be covered by .filter-group select and global .form-control if any */
+}
 </style>
\ No newline at end of file
diff --git a/src/components/__tests__/GanttChartSDST.test.js b/src/components/__tests__/GanttChartSDST.test.js
new file mode 100644
index 0000000..7ec21d6
--- /dev/null
+++ b/src/components/__tests__/GanttChartSDST.test.js
@@ -0,0 +1,163 @@
+import { describe, it, expect, beforeEach, vi } from 'vitest';
+import { mount } from '@vue/test-utils';
+import { createPinia, setActivePinia } from 'pinia';
+import GanttChart from '../GanttChart.vue';
+import { useScheduleStore } from '../../stores/scheduleStore';
+
+describe('GanttChart SDST Enhancement', () => {
+  let wrapper;
+  let scheduleStore;
+
+  beforeEach(() => {
+    const pinia = createPinia();
+    setActivePinia(pinia);
+    scheduleStore = useScheduleStore();
+
+    // Mock the loadInitialData method to prevent automatic loading
+    vi.spyOn(scheduleStore, 'loadInitialData').mockImplementation(() => {});
+
+    // Set up test data directly
+    scheduleStore.scheduledSurgeries = [
+      {
+        id: 's1',
+        patientName: 'John Doe',
+        type: 'Orthopedic',
+        orId: 'or1',
+        startTime: '2023-10-27T08:00:00Z',
+        endTime: '2023-10-27T10:00:00Z',
+        estimatedDuration: 120,
+        sdsTime: 15,
+        duration: 120
+      },
+      {
+        id: 's2',
+        patientName: 'Jane Smith',
+        type: 'Cardiac',
+        orId: 'or1',
+        startTime: '2023-10-27T11:00:00Z',
+        endTime: '2023-10-27T13:00:00Z',
+        estimatedDuration: 120,
+        sdsTime: 30,
+        duration: 120
+      }
+    ];
+
+    scheduleStore.operatingRooms = [
+      { id: 'or1', name: 'OR 1', status: 'Available' },
+      { id: 'or2', name: 'OR 2', status: 'Available' }
+    ];
+
+    scheduleStore.sdsRules = {
+      'Orthopedic': { 'Cardiac': 30, 'General': 15 },
+      'Cardiac': { 'Orthopedic': 45, 'General': 20 },
+      'General': { 'Orthopedic': 15, 'Cardiac': 25 }
+    };
+
+    scheduleStore.initialSetupTimes = {
+      'Orthopedic': 15,
+      'Cardiac': 20,
+      'General': 10
+    };
+
+    scheduleStore.currentDateRange = {
+      start: new Date('2023-10-27T07:00:00Z'),
+      end: new Date('2023-10-27T19:00:00Z')
+    };
+
+    wrapper = mount(GanttChart, {
+      global: {
+        plugins: [pinia]
+      }
+    });
+  });
+
+  it('calculates SDST correctly for real-time positioning', () => {
+    const testSurgery = {
+      id: 's3',
+      type: 'General',
+      estimatedDuration: 90,
+      patientName: 'Test Patient'
+    };
+
+    // Test positioning after an existing surgery (after s1 ends at 10:00)
+    const proposedTime = new Date('2023-10-27T10:30:00Z');
+    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', proposedTime);
+
+    // Should calculate SDST from Orthopedic to General (15 minutes)
+    expect(result.sdsTime).toBe(15);
+    // May have conflicts due to overlap with s2 or other issues, so let's check the actual result
+    console.log('SDST Test Result:', result);
+  });
+
+  it('detects SDST violations correctly', () => {
+    const testSurgery = {
+      id: 's3',
+      type: 'Cardiac',
+      estimatedDuration: 90,
+      patientName: 'Test Patient'
+    };
+
+    // Test positioning too close to existing surgery (SDST violation)
+    const proposedTime = new Date('2023-10-27T10:15:00Z'); // Only 15 min after s1 ends
+    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', proposedTime);
+
+    // Should require 30 minutes SDST from Orthopedic to Cardiac
+    expect(result.sdsTime).toBe(30);
+    console.log('SDST Violation Test Result:', result);
+  });
+
+  it('detects time conflicts correctly', () => {
+    const testSurgery = {
+      id: 's3',
+      type: 'General',
+      estimatedDuration: 90,
+      patientName: 'Test Patient'
+    };
+
+    // Test positioning that overlaps with existing surgery
+    const proposedTime = new Date('2023-10-27T09:00:00Z'); // Overlaps with s1
+    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', proposedTime);
+
+    console.log('Time Conflict Test Result:', result);
+    expect(result.conflicts.length).toBeGreaterThan(0);
+  });
+
+  it('calculates initial setup time for first surgery of the day', () => {
+    const testSurgery = {
+      id: 's3',
+      type: 'Cardiac',
+      estimatedDuration: 90,
+      patientName: 'Test Patient'
+    };
+
+    // Test positioning as first surgery in OR 2 (no existing surgeries)
+    const proposedTime = new Date('2023-10-27T07:00:00Z');
+    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or2', proposedTime);
+
+    // Should use initial setup time for Cardiac (20 minutes)
+    expect(result.sdsTime).toBe(20);
+    console.log('Initial Setup Test Result:', result);
+  });
+
+  it('verifies SDST calculation method exists', () => {
+    // Test that the new method exists and is callable
+    expect(typeof scheduleStore.calculateSDSTForPosition).toBe('function');
+
+    const testSurgery = { id: 'test', type: 'General', estimatedDuration: 60 };
+    const result = scheduleStore.calculateSDSTForPosition(testSurgery, 'or1', new Date());
+
+    expect(result).toHaveProperty('sdsTime');
+    expect(result).toHaveProperty('conflicts');
+    expect(Array.isArray(result.conflicts)).toBe(true);
+  });
+
+  it('shows SDST information in surgery blocks', () => {
+    // Check that surgery blocks display SDST information
+    const surgeryBlocks = wrapper.findAll('.surgery-block');
+    expect(surgeryBlocks.length).toBeGreaterThan(0);
+
+    // Should have SDST segments
+    const sdstSegments = wrapper.findAll('.sdst-segment');
+    expect(sdstSegments.length).toBeGreaterThan(0);
+  });
+});
diff --git a/src/components/__tests__/OptimizationEngine.test.js b/src/components/__tests__/OptimizationEngine.test.js
new file mode 100644
index 0000000..4acb1de
--- /dev/null
+++ b/src/components/__tests__/OptimizationEngine.test.js
@@ -0,0 +1,88 @@
+import { describe, it, expect, beforeEach, vi } from 'vitest';
+import { mount } from '@vue/test-utils';
+import { createPinia, setActivePinia } from 'pinia';
+import OptimizationEngine from '../OptimizationEngine.vue';
+
+// Create a simple mock for the optimization store
+const mockOptimizationStore = () => ({
+  isOptimizing: false,
+  optimizationResults: null,
+  selectedSuggestions: [],
+  optimizationSettings: {
+    prioritizeSDST: true,
+    prioritizeUrgency: true,
+    prioritizeResourceUtilization: true,
+    allowMinorDelays: true,
+    maxDelayMinutes: 30,
+    preserveEmergencies: true,
+  },
+  optimizationHistory: [],
+  currentSuggestions: [],
+  potentialSavings: {
+    sdstReduction: 0,
+    utilizationImprovement: 0,
+    conflictReduction: 0,
+    timesSaved: 0,
+  },
+  optimizationSummary: {
+    totalSuggestions: 0,
+    highPriority: 0,
+    mediumPriority: 0,
+    lowPriority: 0,
+    estimatedImpact: 'Low',
+  },
+  canOptimize: true,
+  runOptimization: vi.fn(),
+  clearOptimizationResults: vi.fn(),
+  updateOptimizationSettings: vi.fn(),
+  toggleSuggestionSelection: vi.fn(),
+  selectAllSuggestions: vi.fn(),
+  clearAllSelections: vi.fn(),
+  applySuggestions: vi.fn(),
+});
+
+// Mock the stores
+vi.mock('@/stores/optimizationStore', () => ({
+  useOptimizationStore: () => mockOptimizationStore()
+}));
+
+describe('OptimizationEngine', () => {
+  let wrapper;
+
+  beforeEach(() => {
+    // Create a fresh Pinia instance for each test
+    const pinia = createPinia();
+    setActivePinia(pinia);
+
+    wrapper = mount(OptimizationEngine, {
+      global: {
+        plugins: [pinia],
+      },
+    });
+  });
+
+  it('renders correctly', () => {
+    expect(wrapper.find('h1').text()).toBe('Schedule Optimization Engine');
+    expect(wrapper.find('.optimization-controls').exists()).toBe(true);
+  });
+
+  it('renders optimization settings', () => {
+    // Check for specific settings
+    expect(wrapper.text()).toContain('Prioritize SDST Reduction');
+    expect(wrapper.text()).toContain('Respect Surgery Urgency');
+    expect(wrapper.text()).toContain('Optimize Resource Utilization');
+    expect(wrapper.text()).toContain('Allow Minor Delays');
+  });
+
+  it('shows run optimization button', () => {
+    const runButton = wrapper.find('.run-optimization-btn');
+    expect(runButton.exists()).toBe(true);
+  });
+
+  it('shows no results state initially', () => {
+    expect(wrapper.find('.no-results').exists()).toBe(true);
+    expect(wrapper.text()).toContain('Ready to Optimize');
+    expect(wrapper.text()).toContain('Reduce Surgery-to-Surgery Transition (SDST) times');
+  });
+
+});
diff --git a/src/components/__tests__/OptimizationSuggestions.test.js b/src/components/__tests__/OptimizationSuggestions.test.js
new file mode 100644
index 0000000..c06b256
--- /dev/null
+++ b/src/components/__tests__/OptimizationSuggestions.test.js
@@ -0,0 +1,322 @@
+import { describe, it, expect, beforeEach, vi } from 'vitest';
+import { mount } from '@vue/test-utils';
+import { createPinia, setActivePinia } from 'pinia';
+import OptimizationSuggestions from '../OptimizationSuggestions.vue';
+import { useOptimizationStore } from '@/stores/optimizationStore';
+
+// Mock the stores
+vi.mock('@/stores/optimizationStore');
+
+describe('OptimizationSuggestions', () => {
+  let wrapper;
+  let optimizationStore;
+
+  beforeEach(() => {
+    // Create a fresh Pinia instance for each test
+    const pinia = createPinia();
+    setActivePinia(pinia);
+
+    // Mock the optimization store
+    optimizationStore = {
+      isOptimizing: false,
+      optimizationResults: null,
+      currentSuggestions: [],
+      runOptimization: vi.fn(),
+      applySuggestions: vi.fn(),
+    };
+
+    // Mock the useOptimizationStore function
+    useOptimizationStore.mockReturnValue(optimizationStore);
+
+    wrapper = mount(OptimizationSuggestions, {
+      global: {
+        plugins: [pinia],
+        stubs: {
+          'router-link': {
+            template: '<a><slot /></a>',
+            props: ['to']
+          }
+        }
+      },
+    });
+  });
+
+  it('renders correctly', () => {
+    expect(wrapper.find('.optimization-suggestions').exists()).toBe(true);
+    expect(wrapper.find('h3').text()).toContain('Optimization Suggestions');
+  });
+
+  it('shows initial state when no optimization results', () => {
+    expect(wrapper.find('.initial-state').exists()).toBe(true);
+    expect(wrapper.text()).toContain('Ready to Optimize');
+    expect(wrapper.text()).toContain('Reduce SDST times');
+    expect(wrapper.text()).toContain('Improve resource utilization');
+    expect(wrapper.text()).toContain('Resolve conflicts');
+  });
+
+  it('shows quick optimize button in initial state', () => {
+    const quickBtn = wrapper.find('.quick-optimize-btn');
+    expect(quickBtn.exists()).toBe(true);
+    expect(quickBtn.text()).toBe('Quick Optimize');
+    expect(quickBtn.attributes('disabled')).toBeUndefined();
+  });
+
+  it('shows view all link', () => {
+    const viewAllLink = wrapper.find('.view-all-link');
+    expect(viewAllLink.exists()).toBe(true);
+    expect(viewAllLink.text()).toBe('View All');
+  });
+
+  it('calls runOptimization when quick optimize button is clicked', async () => {
+    const quickBtn = wrapper.find('.quick-optimize-btn');
+    await quickBtn.trigger('click');
+    
+    expect(optimizationStore.runOptimization).toHaveBeenCalled();
+  });
+
+  it('shows loading state when optimizing', async () => {
+    optimizationStore.isOptimizing = true;
+    await wrapper.vm.$nextTick();
+    
+    expect(wrapper.find('.loading-state').exists()).toBe(true);
+    expect(wrapper.text()).toContain('Analyzing schedule for optimization opportunities...');
+    expect(wrapper.find('.loading-spinner').exists()).toBe(true);
+  });
+
+  it('disables quick optimize button when optimizing', async () => {
+    optimizationStore.isOptimizing = true;
+    await wrapper.vm.$nextTick();
+    
+    const quickBtn = wrapper.find('.quick-optimize-btn');
+    expect(quickBtn.attributes('disabled')).toBeDefined();
+    expect(quickBtn.text()).toContain('Analyzing...');
+  });
+
+  it('shows suggestions when available', async () => {
+    const mockSuggestions = [
+      {
+        id: 'test-1',
+        type: 'reorder',
+        category: 'SDST Optimization',
+        priority: 'High',
+        title: 'Test Suggestion 1',
+        description: 'Test description 1',
+        impact: 30,
+        effort: 'Low',
+        estimatedSavings: '30 minutes',
+        risks: [],
+      },
+      {
+        id: 'test-2',
+        type: 'relocate',
+        category: 'Resource Optimization',
+        priority: 'Medium',
+        title: 'Test Suggestion 2',
+        description: 'Test description 2',
+        impact: 20,
+        effort: 'Medium',
+        estimatedSavings: '20 minutes',
+        risks: ['Minor risk'],
+      }
+    ];
+
+    optimizationStore.optimizationResults = { suggestions: mockSuggestions };
+    optimizationStore.currentSuggestions = mockSuggestions;
+    
+    await wrapper.vm.$nextTick();
+    
+    expect(wrapper.find('.suggestions-list').exists()).toBe(true);
+    
+    const suggestionItems = wrapper.findAll('.suggestion-item');
+    expect(suggestionItems.length).toBe(2);
+    
+    // Check first suggestion
+    expect(suggestionItems[0].text()).toContain('Test Suggestion 1');
+    expect(suggestionItems[0].text()).toContain('Test description 1');
+    expect(suggestionItems[0].text()).toContain('30 min');
+    expect(suggestionItems[0].classes()).toContain('priority-high');
+    
+    // Check second suggestion
+    expect(suggestionItems[1].text()).toContain('Test Suggestion 2');
+    expect(suggestionItems[1].text()).toContain('Test description 2');
+    expect(suggestionItems[1].text()).toContain('20 min');
+    expect(suggestionItems[1].classes()).toContain('priority-medium');
+  });
+
+  it('limits suggestions to top 3', async () => {
+    const mockSuggestions = Array.from({ length: 5 }, (_, i) => ({
+      id: `test-${i + 1}`,
+      type: 'reorder',
+      category: 'SDST Optimization',
+      priority: 'High',
+      title: `Test Suggestion ${i + 1}`,
+      description: `Test description ${i + 1}`,
+      impact: 30 - i * 5,
+      effort: 'Low',
+      estimatedSavings: `${30 - i * 5} minutes`,
+      risks: [],
+    }));
+
+    optimizationStore.optimizationResults = { suggestions: mockSuggestions };
+    optimizationStore.currentSuggestions = mockSuggestions;
+    
+    await wrapper.vm.$nextTick();
+    
+    const suggestionItems = wrapper.findAll('.suggestion-item');
+    expect(suggestionItems.length).toBe(3); // Should only show top 3
+  });
+
+  it('shows no suggestions state when optimization results exist but no suggestions', async () => {
+    optimizationStore.optimizationResults = { suggestions: [] };
+    optimizationStore.currentSuggestions = [];
+    
+    await wrapper.vm.$nextTick();
+    
+    expect(wrapper.find('.no-suggestions').exists()).toBe(true);
+    expect(wrapper.text()).toContain('Schedule Optimized!');
+    expect(wrapper.text()).toContain('No immediate improvements found');
+    expect(wrapper.find('.success-icon').exists()).toBe(true);
+  });
+
+  it('handles apply suggestion correctly', async () => {
+    const mockSuggestion = {
+      id: 'test-1',
+      type: 'reorder',
+      category: 'SDST Optimization',
+      priority: 'High',
+      title: 'Test Suggestion',
+      description: 'Test description',
+      impact: 30,
+      effort: 'Low',
+      estimatedSavings: '30 minutes',
+      risks: [],
+    };
+
+    optimizationStore.optimizationResults = { suggestions: [mockSuggestion] };
+    optimizationStore.currentSuggestions = [mockSuggestion];
+    
+    await wrapper.vm.$nextTick();
+    
+    const applyBtn = wrapper.find('.apply-btn');
+    expect(applyBtn.exists()).toBe(true);
+    
+    await applyBtn.trigger('click');
+    
+    expect(optimizationStore.applySuggestions).toHaveBeenCalledWith(['test-1']);
+  });
+
+  it('shows priority badges correctly', async () => {
+    const mockSuggestions = [
+      {
+        id: 'high-priority',
+        priority: 'High',
+        title: 'High Priority Suggestion',
+        description: 'High priority description',
+        impact: 30,
+        effort: 'Low',
+        estimatedSavings: '30 minutes',
+        risks: [],
+      },
+      {
+        id: 'medium-priority',
+        priority: 'Medium',
+        title: 'Medium Priority Suggestion',
+        description: 'Medium priority description',
+        impact: 20,
+        effort: 'Medium',
+        estimatedSavings: '20 minutes',
+        risks: [],
+      },
+      {
+        id: 'low-priority',
+        priority: 'Low',
+        title: 'Low Priority Suggestion',
+        description: 'Low priority description',
+        impact: 10,
+        effort: 'Low',
+        estimatedSavings: '10 minutes',
+        risks: [],
+      }
+    ];
+
+    optimizationStore.optimizationResults = { suggestions: mockSuggestions };
+    optimizationStore.currentSuggestions = mockSuggestions;
+    
+    await wrapper.vm.$nextTick();
+    
+    const priorityBadges = wrapper.findAll('.priority-badge');
+    expect(priorityBadges.length).toBe(3);
+    
+    expect(priorityBadges[0].classes()).toContain('priority-high');
+    expect(priorityBadges[0].text()).toBe('High');
+    
+    expect(priorityBadges[1].classes()).toContain('priority-medium');
+    expect(priorityBadges[1].text()).toBe('Medium');
+    
+    expect(priorityBadges[2].classes()).toContain('priority-low');
+    expect(priorityBadges[2].text()).toBe('Low');
+  });
+
+  it('shows impact values correctly', async () => {
+    const mockSuggestion = {
+      id: 'test-1',
+      priority: 'High',
+      title: 'Test Suggestion',
+      description: 'Test description',
+      impact: 45,
+      effort: 'Low',
+      estimatedSavings: '45 minutes SDST reduction',
+      risks: [],
+    };
+
+    optimizationStore.optimizationResults = { suggestions: [mockSuggestion] };
+    optimizationStore.currentSuggestions = [mockSuggestion];
+    
+    await wrapper.vm.$nextTick();
+    
+    const impactElement = wrapper.find('.impact');
+    expect(impactElement.text()).toBe('45 min');
+    
+    const savingsElement = wrapper.find('.savings');
+    expect(savingsElement.text()).toBe('45 minutes SDST reduction');
+  });
+
+  it('handles async apply suggestion with loading state', async () => {
+    const mockSuggestion = {
+      id: 'test-1',
+      priority: 'High',
+      title: 'Test Suggestion',
+      description: 'Test description',
+      impact: 30,
+      effort: 'Low',
+      estimatedSavings: '30 minutes',
+      risks: [],
+    };
+
+    optimizationStore.optimizationResults = { suggestions: [mockSuggestion] };
+    optimizationStore.currentSuggestions = [mockSuggestion];
+    
+    // Mock applySuggestions to return a promise
+    optimizationStore.applySuggestions.mockImplementation(() => 
+      new Promise(resolve => setTimeout(resolve, 100))
+    );
+    
+    await wrapper.vm.$nextTick();
+    
+    const applyBtn = wrapper.find('.apply-btn');
+    
+    // Click the button
+    const clickPromise = applyBtn.trigger('click');
+    
+    // Button should be disabled during async operation
+    await wrapper.vm.$nextTick();
+    expect(applyBtn.attributes('disabled')).toBeDefined();
+    
+    // Wait for the async operation to complete
+    await clickPromise;
+    await wrapper.vm.$nextTick();
+    
+    // Button should be enabled again
+    expect(applyBtn.attributes('disabled')).toBeUndefined();
+  });
+});
diff --git a/src/main.js b/src/main.js
index 5e4ca5d..349b2da 100644
--- a/src/main.js
+++ b/src/main.js
@@ -5,6 +5,7 @@ import App from './App.vue'
 import router from './router' // Import the router instance
 import 'vue-toastification/dist/index.css'; // Import the CSS FIRST
 import Toast from 'vue-toastification'; // Import vue-toastification
+import ganttastic from '@infectoone/vue-ganttastic'; // Import vue-ganttastic
 
 const app = createApp(App);
 const pinia = createPinia(); // Create Pinia instance
@@ -25,5 +26,6 @@ try {
 app.use(pinia); // Install Pinia BEFORE mounting
 app.use(router); // Use the router
 app.use(Toast); // Use vue-toastification
+app.use(ganttastic); // Use vue-ganttastic
 
 app.mount('#app');
\ No newline at end of file
diff --git a/src/router/index.js b/src/router/index.js
index 8bcad82..081439e 100644
--- a/src/router/index.js
+++ b/src/router/index.js
@@ -18,6 +18,7 @@ import AdministrationScreen from '../components/AdministrationScreen.vue';
 import MyProfileSettingsScreen from '../components/MyProfileSettingsScreen.vue';
 import HelpDocumentationScreen from '../components/HelpDocumentationScreen.vue';
 import NotFound from '../components/NotFound.vue';
+import MobileTestComponent from '../components/MobileTestComponent.vue';
 
 // Import the authentication store
 import { useAuthStore } from '@/stores/authStore';
@@ -51,6 +52,11 @@ const routes = [
             name: 'Scheduling',
             component: SchedulingScreen,
         },
+        {
+            path: '/master-schedule',
+            name: 'MasterSchedule',
+            component: () => import('../components/MasterScheduleScreen.vue'),
+        },
         {
             path: '/resource-management',
             name: 'ResourceManagement',
@@ -97,6 +103,11 @@ const routes = [
             name: 'Administration',
             component: AdministrationScreen,
         },
+        {
+            path: '/patient-management',
+            name: 'PatientManagement',
+            component: () => import('../components/PatientManagementScreen.vue'),
+        },
         {
             path: '/my-profile-settings',
             name: 'MyProfileSettings',
@@ -106,6 +117,16 @@ const routes = [
             path: '/help-documentation',
             name: 'HelpDocumentation',
             component: HelpDocumentationScreen,
+        },
+        {
+            path: '/optimization',
+            name: 'OptimizationEngine',
+            component: () => import('../components/OptimizationEngine.vue'),
+        },
+        {
+            path: '/mobile-test',
+            name: 'MobileTest',
+            component: MobileTestComponent,
         },
          // Add a redirect for the base authenticated path if needed
         {
diff --git a/src/stores/analyticsStore.js b/src/stores/analyticsStore.js
index 78d2ca5..df60921 100644
--- a/src/stores/analyticsStore.js
+++ b/src/stores/analyticsStore.js
@@ -6,13 +6,13 @@ export const useAnalyticsStore = defineStore('analytics', {
   state: () => ({
     isLoading: false,
     error: null,
-    
+
     // Date range for analytics
     dateRange: {
       start: new Date(new Date().setDate(new Date().getDate() - 30)), // Default to last 30 days
       end: new Date(),
     },
-    
+
     // Cached analytics data
     cachedData: {
       orUtilization: null,
@@ -20,17 +20,21 @@ export const useAnalyticsStore = defineStore('analytics', {
       surgeryTypeDistribution: null,
       sdstEfficiency: null,
       dailyMetrics: null,
+      sdstPatterns: null,
+      resourceOptimization: null,
+      schedulingEfficiency: null,
+      conflictAnalysis: null,
     },
-    
+
     // Custom report configurations
     savedReports: [
       {
         id: 'report-1',
         name: 'Monthly OR Utilization',
         type: 'orUtilization',
-        dateRange: { 
-          start: new Date(new Date().setMonth(new Date().getMonth() - 1)), 
-          end: new Date() 
+        dateRange: {
+          start: new Date(new Date().setMonth(new Date().getMonth() - 1)),
+          end: new Date()
         },
         filters: { orIds: ['OR1', 'OR2', 'OR3'] },
         metrics: ['utilizationRate', 'idleTime', 'overtimeRate'],
@@ -40,9 +44,9 @@ export const useAnalyticsStore = defineStore('analytics', {
         id: 'report-2',
         name: 'Surgeon Performance',
         type: 'surgeonUtilization',
-        dateRange: { 
-          start: new Date(new Date().setMonth(new Date().getMonth() - 3)), 
-          end: new Date() 
+        dateRange: {
+          start: new Date(new Date().setMonth(new Date().getMonth() - 3)),
+          end: new Date()
         },
         filters: { surgeonIds: ['SG1', 'SG2', 'SG3'] },
         metrics: ['surgeryCount', 'averageDuration', 'onTimeStart'],
@@ -50,73 +54,144 @@ export const useAnalyticsStore = defineStore('analytics', {
       },
     ],
   }),
-  
+
   getters: {
     // Get the schedule and resource stores
     scheduleStore: () => useScheduleStore(),
     resourceStore: () => useResourceStore(),
-    
+
     // Get OR utilization data
     orUtilization: (state) => {
       if (state.cachedData.orUtilization) {
         return state.cachedData.orUtilization;
       }
-      
+
       // If not cached, calculate it (this would normally be fetched from an API)
       return null;
     },
-    
+
     // Get surgeon utilization data
     surgeonUtilization: (state) => {
       if (state.cachedData.surgeonUtilization) {
         return state.cachedData.surgeonUtilization;
       }
-      
+
       // If not cached, calculate it (this would normally be fetched from an API)
       return null;
     },
-    
+
     // Get surgery type distribution data
     surgeryTypeDistribution: (state) => {
       if (state.cachedData.surgeryTypeDistribution) {
         return state.cachedData.surgeryTypeDistribution;
       }
-      
+
       // If not cached, calculate it (this would normally be fetched from an API)
       return null;
     },
-    
+
     // Get SDST efficiency data
     sdstEfficiency: (state) => {
       if (state.cachedData.sdstEfficiency) {
         return state.cachedData.sdstEfficiency;
       }
-      
+
       // If not cached, calculate it (this would normally be fetched from an API)
       return null;
     },
-    
+
     // Get daily metrics data
     dailyMetrics: (state) => {
       if (state.cachedData.dailyMetrics) {
         return state.cachedData.dailyMetrics;
       }
-      
+
       // If not cached, calculate it (this would normally be fetched from an API)
       return null;
     },
+
+    // Get SDST patterns data
+    sdstPatterns: (state) => {
+      if (state.cachedData.sdstPatterns) {
+        return state.cachedData.sdstPatterns;
+      }
+      return null;
+    },
+
+    // Get resource optimization data
+    resourceOptimization: (state) => {
+      if (state.cachedData.resourceOptimization) {
+        return state.cachedData.resourceOptimization;
+      }
+      return null;
+    },
+
+    // Get scheduling efficiency data
+    schedulingEfficiency: (state) => {
+      if (state.cachedData.schedulingEfficiency) {
+        return state.cachedData.schedulingEfficiency;
+      }
+      return null;
+    },
+
+    // Get conflict analysis data
+    conflictAnalysis: (state) => {
+      if (state.cachedData.conflictAnalysis) {
+        return state.cachedData.conflictAnalysis;
+      }
+      return null;
+    },
+
+    // Get key performance indicators
+    keyPerformanceIndicators: (state) => {
+      const orUtil = state.cachedData.orUtilization;
+      const schedEff = state.cachedData.schedulingEfficiency;
+      const sdstEff = state.cachedData.sdstEfficiency;
+
+      if (!orUtil || !schedEff || !sdstEff) return null;
+
+      return {
+        averageORUtilization: orUtil.reduce((sum, or) => sum + or.utilizationRate, 0) / orUtil.length,
+        onTimeStartRate: schedEff.onTimeStartRate?.overall || 0,
+        averageSDST: sdstEff.averageSDST || 0,
+        conflictRate: state.cachedData.conflictAnalysis?.conflictFrequency?.daily || 0,
+      };
+    },
+
+    // Get optimization opportunities
+    optimizationOpportunities: (state) => {
+      const opportunities = [];
+
+      if (state.cachedData.sdstPatterns?.optimizationOpportunities) {
+        opportunities.push(...state.cachedData.sdstPatterns.optimizationOpportunities);
+      }
+
+      if (state.cachedData.resourceOptimization?.recommendations) {
+        opportunities.push(...state.cachedData.resourceOptimization.recommendations.map(rec => ({
+          type: rec.category,
+          description: rec.recommendation,
+          potentialSavings: rec.impact,
+          priority: rec.priority,
+        })));
+      }
+
+      return opportunities.sort((a, b) => {
+        const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
+        return priorityOrder[b.priority] - priorityOrder[a.priority];
+      });
+    },
   },
-  
+
   actions: {
     // Set the date range for analytics
     setDateRange(start, end) {
       this.dateRange.start = start;
       this.dateRange.end = end;
-      
+
       // Clear cached data when date range changes
       this.clearCachedData();
     },
-    
+
     // Clear cached data
     clearCachedData() {
       this.cachedData = {
@@ -125,14 +200,18 @@ export const useAnalyticsStore = defineStore('analytics', {
         surgeryTypeDistribution: null,
         sdstEfficiency: null,
         dailyMetrics: null,
+        sdstPatterns: null,
+        resourceOptimization: null,
+        schedulingEfficiency: null,
+        conflictAnalysis: null,
       };
     },
-    
+
     // Load analytics data
     async loadAnalyticsData() {
       this.isLoading = true;
       this.error = null;
-      
+
       try {
         // In a real app, this would fetch data from an API
         await this.simulateLoadORUtilization();
@@ -140,8 +219,12 @@ export const useAnalyticsStore = defineStore('analytics', {
         await this.simulateLoadSurgeryTypeDistribution();
         await this.simulateLoadSDSTEfficiency();
         await this.simulateLoadDailyMetrics();
-        
-        console.log('Analytics data loaded successfully');
+        await this.simulateLoadSDSTPatterns();
+        await this.simulateLoadResourceOptimization();
+        await this.simulateLoadSchedulingEfficiency();
+        await this.simulateLoadConflictAnalysis();
+
+        console.log('Enhanced analytics data loaded successfully');
       } catch (error) {
         this.error = 'Failed to load analytics data';
         console.error('Failed to load analytics data:', error);
@@ -149,27 +232,27 @@ export const useAnalyticsStore = defineStore('analytics', {
         this.isLoading = false;
       }
     },
-    
+
     // Simulate loading OR utilization data
     async simulateLoadORUtilization() {
       // Simulate API call
       await new Promise(resolve => setTimeout(resolve, 500));
-      
+
       // Generate mock data
       const orUtilization = [];
       const scheduleStore = useScheduleStore();
       const resourceStore = useResourceStore();
-      
+
       resourceStore.operatingRooms.forEach(or => {
         // Calculate utilization based on scheduled surgeries
         const surgeries = scheduleStore.scheduledSurgeries.filter(s => s.orId === or.id);
         const totalMinutes = surgeries.reduce((total, s) => total + s.duration, 0);
         const totalHours = totalMinutes / 60;
-        
+
         // Assume 8-hour workday
         const workdayHours = 8;
         const utilizationRate = Math.min(totalHours / workdayHours, 1);
-        
+
         orUtilization.push({
           orId: or.id,
           orName: or.name,
@@ -180,20 +263,20 @@ export const useAnalyticsStore = defineStore('analytics', {
           overtimeRate: Math.max(0, (totalHours - workdayHours) / workdayHours),
         });
       });
-      
+
       this.cachedData.orUtilization = orUtilization;
     },
-    
+
     // Simulate loading surgeon utilization data
     async simulateLoadSurgeonUtilization() {
       // Simulate API call
       await new Promise(resolve => setTimeout(resolve, 500));
-      
+
       // Generate mock data
       const surgeonUtilization = [];
       const scheduleStore = useScheduleStore();
       const resourceStore = useResourceStore();
-      
+
       resourceStore.staff
         .filter(s => s.role === 'Surgeon')
         .forEach(surgeon => {
@@ -202,13 +285,13 @@ export const useAnalyticsStore = defineStore('analytics', {
           const totalMinutes = surgeries.reduce((total, s) => total + s.duration, 0);
           const totalHours = totalMinutes / 60;
           const surgeryCount = surgeries.length;
-          
+
           // Calculate average duration
           const averageDuration = surgeryCount > 0 ? totalMinutes / surgeryCount : 0;
-          
+
           // Simulate on-time start percentage (would be calculated from actual data)
           const onTimeStart = Math.random() * 0.3 + 0.7; // Random between 70% and 100%
-          
+
           surgeonUtilization.push({
             surgeonId: surgeon.id,
             surgeonName: surgeon.name,
@@ -218,27 +301,27 @@ export const useAnalyticsStore = defineStore('analytics', {
             onTimeStart,
           });
         });
-      
+
       this.cachedData.surgeonUtilization = surgeonUtilization;
     },
-    
+
     // Simulate loading surgery type distribution data
     async simulateLoadSurgeryTypeDistribution() {
       // Simulate API call
       await new Promise(resolve => setTimeout(resolve, 500));
-      
+
       // Generate mock data
       const surgeryTypeDistribution = [];
       const scheduleStore = useScheduleStore();
-      
+
       // Get unique surgery types
       const surgeryTypes = [...new Set(scheduleStore.scheduledSurgeries.map(s => s.type))];
-      
+
       surgeryTypes.forEach(type => {
         const surgeries = scheduleStore.scheduledSurgeries.filter(s => s.type === type);
         const count = surgeries.length;
         const totalMinutes = surgeries.reduce((total, s) => total + s.duration, 0);
-        
+
         surgeryTypeDistribution.push({
           type,
           count,
@@ -247,15 +330,15 @@ export const useAnalyticsStore = defineStore('analytics', {
           percentage: count / scheduleStore.scheduledSurgeries.length,
         });
       });
-      
+
       this.cachedData.surgeryTypeDistribution = surgeryTypeDistribution;
     },
-    
+
     // Simulate loading SDST efficiency data
     async simulateLoadSDSTEfficiency() {
       // Simulate API call
       await new Promise(resolve => setTimeout(resolve, 500));
-      
+
       // Generate mock data for SDST efficiency
       const sdstEfficiency = {
         averageSDST: 22.5, // minutes
@@ -272,24 +355,24 @@ export const useAnalyticsStore = defineStore('analytics', {
         },
         potentialSavings: 120, // minutes per day
       };
-      
+
       this.cachedData.sdstEfficiency = sdstEfficiency;
     },
-    
+
     // Simulate loading daily metrics data
     async simulateLoadDailyMetrics() {
       // Simulate API call
       await new Promise(resolve => setTimeout(resolve, 500));
-      
+
       // Generate mock data for daily metrics over the last 30 days
       const dailyMetrics = [];
       const startDate = new Date(this.dateRange.start);
       const endDate = new Date(this.dateRange.end);
-      
+
       // Loop through each day in the date range
       for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
         const date = new Date(d);
-        
+
         // Generate random metrics for the day
         dailyMetrics.push({
           date: date.toISOString().split('T')[0],
@@ -299,27 +382,442 @@ export const useAnalyticsStore = defineStore('analytics', {
           averageTurnaround: Math.floor(Math.random() * 10) + 20, // 20-30 minutes
         });
       }
-      
+
       this.cachedData.dailyMetrics = dailyMetrics;
     },
-    
+
     // Save a custom report configuration
     saveCustomReport(reportConfig) {
       // Generate a unique ID
       const newId = `report-${Date.now()}`;
-      
+
       // Add the new report
       this.savedReports.push({
         id: newId,
         ...reportConfig,
       });
-      
+
       return newId;
     },
-    
+
     // Delete a custom report
     deleteCustomReport(reportId) {
       this.savedReports = this.savedReports.filter(r => r.id !== reportId);
     },
+
+    // Simulate loading SDST patterns data
+    async simulateLoadSDSTPatterns() {
+      await new Promise(resolve => setTimeout(resolve, 300));
+
+      try {
+        const scheduleStore = useScheduleStore();
+        const surgeries = scheduleStore.scheduledSurgeries || [];
+
+        // Analyze SDST patterns
+        const sdstPatterns = {
+          transitionMatrix: this.calculateSDSTTransitionMatrix(surgeries),
+          timeOfDayPatterns: this.calculateTimeOfDaySDSTPatterns(surgeries),
+          orSpecificPatterns: this.calculateORSpecificSDSTPatterns(surgeries),
+          optimizationOpportunities: this.identifySDSTOptimizationOpportunities(surgeries),
+        };
+
+        this.cachedData.sdstPatterns = sdstPatterns;
+      } catch (error) {
+        console.warn('Failed to load SDST patterns:', error);
+        // Provide fallback data
+        this.cachedData.sdstPatterns = {
+          transitionMatrix: {},
+          timeOfDayPatterns: {},
+          orSpecificPatterns: {},
+          optimizationOpportunities: [],
+        };
+      }
+    },
+
+    // Simulate loading resource optimization data
+    async simulateLoadResourceOptimization() {
+      await new Promise(resolve => setTimeout(resolve, 300));
+
+      try {
+        const scheduleStore = useScheduleStore();
+        const resourceStore = useResourceStore();
+
+        const resourceOptimization = {
+          underutilizedResources: this.identifyUnderutilizedResources(),
+          bottleneckAnalysis: this.analyzeResourceBottlenecks(),
+          staffWorkloadBalance: this.analyzeStaffWorkloadBalance(),
+          equipmentUtilization: this.analyzeEquipmentUtilization(),
+          recommendations: this.generateResourceOptimizationRecommendations(),
+        };
+
+        this.cachedData.resourceOptimization = resourceOptimization;
+      } catch (error) {
+        console.warn('Failed to load resource optimization data:', error);
+        this.cachedData.resourceOptimization = {
+          underutilizedResources: [],
+          bottleneckAnalysis: [],
+          staffWorkloadBalance: { balanced: 0.75, overloaded: 0.15, underutilized: 0.10, recommendations: [] },
+          equipmentUtilization: { highUtilization: [], mediumUtilization: [], lowUtilization: [], maintenanceSchedule: 'Optimal' },
+          recommendations: [],
+        };
+      }
+    },
+
+    // Simulate loading scheduling efficiency data
+    async simulateLoadSchedulingEfficiency() {
+      await new Promise(resolve => setTimeout(resolve, 300));
+
+      const scheduleStore = useScheduleStore();
+
+      const schedulingEfficiency = {
+        onTimeStartRate: this.calculateOnTimeStartRate(),
+        averageTurnaroundTime: this.calculateAverageTurnaroundTime(),
+        scheduleAdherence: this.calculateScheduleAdherence(),
+        emergencyCaseImpact: this.analyzeEmergencyCaseImpact(),
+        cancellationRate: this.calculateCancellationRate(),
+        efficiencyTrends: this.calculateEfficiencyTrends(),
+      };
+
+      this.cachedData.schedulingEfficiency = schedulingEfficiency;
+    },
+
+    // Simulate loading conflict analysis data
+    async simulateLoadConflictAnalysis() {
+      await new Promise(resolve => setTimeout(resolve, 300));
+
+      const scheduleStore = useScheduleStore();
+
+      const conflictAnalysis = {
+        conflictFrequency: this.analyzeConflictFrequency(),
+        conflictTypes: this.categorizeConflictTypes(),
+        resolutionTimes: this.analyzeConflictResolutionTimes(),
+        preventableConflicts: this.identifyPreventableConflicts(),
+        conflictTrends: this.analyzeConflictTrends(),
+      };
+
+      this.cachedData.conflictAnalysis = conflictAnalysis;
+    },
+
+    // Helper methods for enhanced analytics calculations
+    calculateSDSTTransitionMatrix(surgeries) {
+      const transitions = {};
+
+      // Group surgeries by OR and date
+      const orSchedules = {};
+      surgeries.forEach(surgery => {
+        const key = `${surgery.orId}-${surgery.startTime.split('T')[0]}`;
+        if (!orSchedules[key]) orSchedules[key] = [];
+        orSchedules[key].push(surgery);
+      });
+
+      // Analyze transitions within each OR schedule
+      Object.values(orSchedules).forEach(schedule => {
+        schedule.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
+
+        for (let i = 1; i < schedule.length; i++) {
+          const from = schedule[i - 1].type;
+          const to = schedule[i].type;
+          const key = `${from}->${to}`;
+
+          if (!transitions[key]) {
+            transitions[key] = { count: 0, totalTime: 0, avgTime: 0 };
+          }
+
+          transitions[key].count++;
+          transitions[key].totalTime += schedule[i].sdsTime || 0;
+          transitions[key].avgTime = transitions[key].totalTime / transitions[key].count;
+        }
+      });
+
+      return transitions;
+    },
+
+    calculateTimeOfDaySDSTPatterns(surgeries) {
+      const patterns = {
+        morning: { count: 0, avgSDST: 0, totalSDST: 0 },
+        afternoon: { count: 0, avgSDST: 0, totalSDST: 0 },
+        evening: { count: 0, avgSDST: 0, totalSDST: 0 },
+      };
+
+      surgeries.forEach(surgery => {
+        const hour = new Date(surgery.startTime).getHours();
+        let period;
+
+        if (hour < 12) period = 'morning';
+        else if (hour < 17) period = 'afternoon';
+        else period = 'evening';
+
+        patterns[period].count++;
+        patterns[period].totalSDST += surgery.sdsTime || 0;
+        patterns[period].avgSDST = patterns[period].totalSDST / patterns[period].count;
+      });
+
+      return patterns;
+    },
+
+    calculateORSpecificSDSTPatterns(surgeries) {
+      const orPatterns = {};
+
+      surgeries.forEach(surgery => {
+        if (!orPatterns[surgery.orId]) {
+          orPatterns[surgery.orId] = {
+            orName: surgery.orName,
+            totalSDST: 0,
+            surgeryCount: 0,
+            avgSDST: 0,
+            maxSDST: 0,
+            minSDST: Infinity,
+          };
+        }
+
+        const pattern = orPatterns[surgery.orId];
+        const sdst = surgery.sdsTime || 0;
+
+        pattern.totalSDST += sdst;
+        pattern.surgeryCount++;
+        pattern.avgSDST = pattern.totalSDST / pattern.surgeryCount;
+        pattern.maxSDST = Math.max(pattern.maxSDST, sdst);
+        pattern.minSDST = Math.min(pattern.minSDST, sdst);
+      });
+
+      return orPatterns;
+    },
+
+    identifySDSTOptimizationOpportunities(surgeries) {
+      return [
+        {
+          type: 'Surgery Sequencing',
+          description: 'Reorder surgeries to minimize SDST transitions',
+          potentialSavings: '45 minutes/day',
+          priority: 'High',
+        },
+        {
+          type: 'OR Specialization',
+          description: 'Dedicate specific ORs to surgery types with high SDST',
+          potentialSavings: '30 minutes/day',
+          priority: 'Medium',
+        },
+        {
+          type: 'Equipment Pre-positioning',
+          description: 'Pre-position equipment for known surgery sequences',
+          potentialSavings: '20 minutes/day',
+          priority: 'Medium',
+        },
+      ];
+    },
+
+    identifyUnderutilizedResources() {
+      return [
+        { type: 'OR', name: 'OR 5', utilization: 0.65, recommendation: 'Consider scheduling more cases' },
+        { type: 'Staff', name: 'Dr. Johnson', utilization: 0.70, recommendation: 'Available for additional surgeries' },
+        { type: 'Equipment', name: 'C-Arm Unit 3', utilization: 0.45, recommendation: 'Underutilized, consider reallocation' },
+      ];
+    },
+
+    analyzeResourceBottlenecks() {
+      return [
+        { resource: 'Anesthesia Team', impact: 'High', description: 'Limited availability causing delays' },
+        { resource: 'Cardiac OR', impact: 'Medium', description: 'High demand for specialized procedures' },
+        { resource: 'Sterilization', impact: 'Low', description: 'Occasional delays in instrument processing' },
+      ];
+    },
+
+    analyzeStaffWorkloadBalance() {
+      return {
+        balanced: 0.75,
+        overloaded: 0.15,
+        underutilized: 0.10,
+        recommendations: [
+          'Redistribute cases from Dr. Smith to Dr. Johnson',
+          'Consider cross-training nurses for multiple specialties',
+        ],
+      };
+    },
+
+    analyzeEquipmentUtilization() {
+      return {
+        highUtilization: ['Phacoemulsification Machine', 'Cardiac Monitor'],
+        mediumUtilization: ['C-Arm Unit 1', 'Laser System'],
+        lowUtilization: ['C-Arm Unit 3', 'Microscope 2'],
+        maintenanceSchedule: 'Optimal',
+      };
+    },
+
+    generateResourceOptimizationRecommendations() {
+      return [
+        {
+          priority: 'High',
+          category: 'Staffing',
+          recommendation: 'Add one additional anesthesia team for peak hours',
+          impact: 'Reduce delays by 25%',
+        },
+        {
+          priority: 'Medium',
+          category: 'Equipment',
+          recommendation: 'Relocate underutilized C-Arm to high-demand OR',
+          impact: 'Improve equipment utilization by 15%',
+        },
+        {
+          priority: 'Low',
+          category: 'Scheduling',
+          recommendation: 'Implement block scheduling for cardiac procedures',
+          impact: 'Reduce SDST by 10%',
+        },
+      ];
+    },
+
+    calculateOnTimeStartRate() {
+      return {
+        overall: 0.82,
+        byOR: {
+          'OR1': 0.85,
+          'OR2': 0.78,
+          'OR3': 0.84,
+          'OR4': 0.80,
+          'OR5': 0.83,
+        },
+        trend: 'improving',
+        target: 0.90,
+      };
+    },
+
+    calculateAverageTurnaroundTime() {
+      return {
+        overall: 24.5, // minutes
+        byType: {
+          'APPEN': 18,
+          'KNEE': 22,
+          'CABG': 35,
+          'CATAR': 15,
+          'HIPRE': 28,
+        },
+        target: 20,
+        trend: 'stable',
+      };
+    },
+
+    calculateScheduleAdherence() {
+      return {
+        onTime: 0.75,
+        delayed: 0.20,
+        early: 0.05,
+        averageDelay: 12, // minutes
+        majorDelays: 0.03, // >30 minutes
+      };
+    },
+
+    analyzeEmergencyCaseImpact() {
+      return {
+        frequency: 2.3, // per day
+        averageDelay: 45, // minutes to scheduled cases
+        cancellationRate: 0.05,
+        resourceReallocation: 'Moderate',
+        recommendations: [
+          'Reserve OR capacity for emergencies',
+          'Implement emergency case protocols',
+        ],
+      };
+    },
+
+    calculateCancellationRate() {
+      return {
+        overall: 0.08,
+        byReason: {
+          'Patient condition': 0.03,
+          'Equipment failure': 0.02,
+          'Staff unavailability': 0.02,
+          'Emergency priority': 0.01,
+        },
+        trend: 'decreasing',
+        target: 0.05,
+      };
+    },
+
+    calculateEfficiencyTrends() {
+      const days = 30;
+      const trends = [];
+
+      for (let i = 0; i < days; i++) {
+        const date = new Date();
+        date.setDate(date.getDate() - i);
+
+        trends.unshift({
+          date: date.toISOString().split('T')[0],
+          efficiency: 0.75 + Math.random() * 0.2, // 75-95%
+          onTimeStarts: 0.80 + Math.random() * 0.15, // 80-95%
+          utilization: 0.70 + Math.random() * 0.25, // 70-95%
+        });
+      }
+
+      return trends;
+    },
+
+    analyzeConflictFrequency() {
+      return {
+        daily: 3.2,
+        weekly: 22.4,
+        monthly: 96.8,
+        trend: 'decreasing',
+        peakTimes: ['Monday morning', 'Friday afternoon'],
+      };
+    },
+
+    categorizeConflictTypes() {
+      return {
+        'Resource conflicts': 0.45,
+        'SDST violations': 0.25,
+        'Staff unavailability': 0.20,
+        'Equipment conflicts': 0.10,
+      };
+    },
+
+    analyzeConflictResolutionTimes() {
+      return {
+        average: 8.5, // minutes
+        byType: {
+          'Resource conflicts': 12,
+          'SDST violations': 6,
+          'Staff unavailability': 15,
+          'Equipment conflicts': 5,
+        },
+        target: 5,
+      };
+    },
+
+    identifyPreventableConflicts() {
+      return {
+        percentage: 0.65,
+        causes: [
+          'Inadequate advance planning',
+          'Lack of real-time resource visibility',
+          'Manual scheduling errors',
+          'Emergency case disruptions',
+        ],
+        solutions: [
+          'Implement automated conflict detection',
+          'Improve resource tracking systems',
+          'Enhanced staff training',
+        ],
+      };
+    },
+
+    analyzeConflictTrends() {
+      const days = 30;
+      const trends = [];
+
+      for (let i = 0; i < days; i++) {
+        const date = new Date();
+        date.setDate(date.getDate() - i);
+
+        trends.unshift({
+          date: date.toISOString().split('T')[0],
+          conflicts: Math.floor(Math.random() * 6) + 1, // 1-6 conflicts per day
+          resolved: Math.floor(Math.random() * 5) + 1, // 1-5 resolved
+          avgResolutionTime: Math.floor(Math.random() * 10) + 5, // 5-15 minutes
+        });
+      }
+
+      return trends;
+    },
   }
 });
diff --git a/src/stores/optimizationStore.js b/src/stores/optimizationStore.js
new file mode 100644
index 0000000..f080c3d
--- /dev/null
+++ b/src/stores/optimizationStore.js
@@ -0,0 +1,535 @@
+import { defineStore } from 'pinia';
+import { useScheduleStore } from './scheduleStore';
+import { useResourceStore } from './resourceStore';
+import { useAnalyticsStore } from './analyticsStore';
+
+export const useOptimizationStore = defineStore('optimization', {
+  state: () => ({
+    // Optimization state
+    isOptimizing: false,
+    optimizationResults: null,
+    selectedSuggestions: [],
+    
+    // Optimization settings
+    optimizationSettings: {
+      prioritizeSDST: true,
+      prioritizeUrgency: true,
+      prioritizeResourceUtilization: true,
+      allowMinorDelays: true,
+      maxDelayMinutes: 30,
+      preserveEmergencies: true,
+    },
+    
+    // Cached optimization data
+    cachedOptimizations: {},
+    
+    // Optimization history
+    optimizationHistory: [],
+    
+    // Performance metrics
+    optimizationMetrics: {
+      totalSuggestionsGenerated: 0,
+      suggestionsApplied: 0,
+      averageSDSTImprovement: 0,
+      averageUtilizationImprovement: 0,
+    },
+  }),
+
+  getters: {
+    // Get current optimization suggestions
+    currentSuggestions: (state) => {
+      return state.optimizationResults?.suggestions || [];
+    },
+
+    // Get high priority suggestions
+    highPrioritySuggestions: (state) => {
+      return state.optimizationResults?.suggestions?.filter(s => s.priority === 'High') || [];
+    },
+
+    // Get potential savings
+    potentialSavings: (state) => {
+      if (!state.optimizationResults) return null;
+      
+      return {
+        sdstReduction: state.optimizationResults.metrics?.sdstReduction || 0,
+        utilizationImprovement: state.optimizationResults.metrics?.utilizationImprovement || 0,
+        conflictReduction: state.optimizationResults.metrics?.conflictReduction || 0,
+        timesSaved: state.optimizationResults.metrics?.timesSaved || 0,
+      };
+    },
+
+    // Get optimization summary
+    optimizationSummary: (state) => {
+      if (!state.optimizationResults) return null;
+      
+      const suggestions = state.optimizationResults.suggestions || [];
+      return {
+        totalSuggestions: suggestions.length,
+        highPriority: suggestions.filter(s => s.priority === 'High').length,
+        mediumPriority: suggestions.filter(s => s.priority === 'Medium').length,
+        lowPriority: suggestions.filter(s => s.priority === 'Low').length,
+        estimatedImpact: state.optimizationResults.metrics?.overallImpact || 'Low',
+      };
+    },
+
+    // Check if optimization is available
+    canOptimize: (state) => {
+      const scheduleStore = useScheduleStore();
+      return !state.isOptimizing && scheduleStore.scheduledSurgeries.length > 0;
+    },
+  },
+
+  actions: {
+    // Run optimization analysis
+    async runOptimization(targetDate = null) {
+      if (this.isOptimizing) return;
+      
+      this.isOptimizing = true;
+      
+      try {
+        const scheduleStore = useScheduleStore();
+        const resourceStore = useResourceStore();
+        const analyticsStore = useAnalyticsStore();
+        
+        // Get current schedule data
+        const surgeries = targetDate 
+          ? scheduleStore.getSurgeriesForDate(targetDate)
+          : scheduleStore.scheduledSurgeries;
+        
+        if (surgeries.length === 0) {
+          throw new Error('No surgeries found for optimization');
+        }
+
+        // Simulate optimization processing
+        await new Promise(resolve => setTimeout(resolve, 2000));
+        
+        // Generate optimization suggestions
+        const suggestions = await this.generateOptimizationSuggestions(surgeries);
+        
+        // Calculate optimization metrics
+        const metrics = this.calculateOptimizationMetrics(surgeries, suggestions);
+        
+        // Store results
+        this.optimizationResults = {
+          timestamp: new Date().toISOString(),
+          targetDate: targetDate || new Date().toISOString().split('T')[0],
+          suggestions,
+          metrics,
+          originalSchedule: [...surgeries],
+        };
+        
+        // Update metrics
+        this.optimizationMetrics.totalSuggestionsGenerated += suggestions.length;
+        
+        // Add to history
+        this.optimizationHistory.unshift({
+          id: Date.now(),
+          timestamp: new Date().toISOString(),
+          suggestionsCount: suggestions.length,
+          potentialSavings: metrics.sdstReduction,
+          applied: false,
+        });
+        
+        // Keep only last 10 optimization runs
+        if (this.optimizationHistory.length > 10) {
+          this.optimizationHistory = this.optimizationHistory.slice(0, 10);
+        }
+        
+        console.log('Optimization completed:', this.optimizationResults);
+        
+      } catch (error) {
+        console.error('Optimization failed:', error);
+        throw error;
+      } finally {
+        this.isOptimizing = false;
+      }
+    },
+
+    // Generate optimization suggestions
+    async generateOptimizationSuggestions(surgeries) {
+      const suggestions = [];
+      
+      // 1. SDST Optimization Suggestions
+      const sdstSuggestions = this.generateSDSTOptimizationSuggestions(surgeries);
+      suggestions.push(...sdstSuggestions);
+      
+      // 2. Resource Utilization Suggestions
+      const resourceSuggestions = this.generateResourceOptimizationSuggestions(surgeries);
+      suggestions.push(...resourceSuggestions);
+      
+      // 3. Conflict Resolution Suggestions
+      const conflictSuggestions = this.generateConflictResolutionSuggestions(surgeries);
+      suggestions.push(...conflictSuggestions);
+      
+      // 4. Schedule Balancing Suggestions
+      const balancingSuggestions = this.generateScheduleBalancingSuggestions(surgeries);
+      suggestions.push(...balancingSuggestions);
+      
+      // Sort by priority and impact
+      return suggestions.sort((a, b) => {
+        const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
+        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
+          return priorityOrder[b.priority] - priorityOrder[a.priority];
+        }
+        return b.impact - a.impact;
+      });
+    },
+
+    // Generate SDST optimization suggestions
+    generateSDSTOptimizationSuggestions(surgeries) {
+      const suggestions = [];
+      
+      // Group surgeries by OR
+      const orGroups = {};
+      surgeries.forEach(surgery => {
+        if (!orGroups[surgery.orId]) orGroups[surgery.orId] = [];
+        orGroups[surgery.orId].push(surgery);
+      });
+      
+      // Analyze each OR for SDST optimization opportunities
+      Object.entries(orGroups).forEach(([orId, orSurgeries]) => {
+        if (orSurgeries.length < 2) return;
+        
+        // Sort by start time
+        orSurgeries.sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
+        
+        // Find inefficient transitions
+        for (let i = 1; i < orSurgeries.length; i++) {
+          const prev = orSurgeries[i - 1];
+          const current = orSurgeries[i];
+          
+          // Check if reordering could reduce SDST
+          const currentSDST = this.getSDSTBetweenTypes(prev.type, current.type);
+          
+          // Look for better alternatives
+          const alternatives = orSurgeries.slice(i).filter(s => 
+            this.getSDSTBetweenTypes(prev.type, s.type) < currentSDST - 10
+          );
+          
+          if (alternatives.length > 0) {
+            const bestAlternative = alternatives[0];
+            const savings = currentSDST - this.getSDSTBetweenTypes(prev.type, bestAlternative.type);
+            
+            suggestions.push({
+              id: `sdst-${Date.now()}-${i}`,
+              type: 'reorder',
+              category: 'SDST Optimization',
+              priority: savings > 20 ? 'High' : savings > 10 ? 'Medium' : 'Low',
+              title: `Reorder surgeries in ${orId} to reduce SDST`,
+              description: `Move ${bestAlternative.type} surgery (${bestAlternative.patientName}) before ${current.type} surgery (${current.patientName}) to save ${savings} minutes of setup time`,
+              impact: savings,
+              effort: 'Low',
+              surgeryIds: [current.id, bestAlternative.id],
+              originalOrder: [current.id, bestAlternative.id],
+              suggestedOrder: [bestAlternative.id, current.id],
+              estimatedSavings: `${savings} minutes SDST reduction`,
+              risks: savings > 15 ? [] : ['Minor schedule disruption'],
+            });
+          }
+        }
+      });
+      
+      return suggestions;
+    },
+
+    // Generate resource optimization suggestions
+    generateResourceOptimizationSuggestions(surgeries) {
+      const suggestions = [];
+      const resourceStore = useResourceStore();
+      
+      // Analyze resource conflicts and underutilization
+      const resourceUsage = this.analyzeResourceUsage(surgeries);
+      
+      // Suggest moving surgeries to underutilized ORs
+      Object.entries(resourceUsage.orUtilization).forEach(([orId, utilization]) => {
+        if (utilization < 0.6) {
+          const overutilizedORs = Object.entries(resourceUsage.orUtilization)
+            .filter(([id, util]) => util > 0.9 && id !== orId);
+          
+          if (overutilizedORs.length > 0) {
+            const [overutilizedOR] = overutilizedORs[0];
+            const movableSurgeries = surgeries.filter(s => 
+              s.orId === overutilizedOR && s.priority !== 'Emergency'
+            );
+            
+            if (movableSurgeries.length > 0) {
+              const surgery = movableSurgeries[0];
+              suggestions.push({
+                id: `resource-${Date.now()}-${orId}`,
+                type: 'relocate',
+                category: 'Resource Optimization',
+                priority: 'Medium',
+                title: `Move surgery to underutilized OR`,
+                description: `Move ${surgery.type} surgery (${surgery.patientName}) from ${overutilizedOR} to ${orId} to balance resource utilization`,
+                impact: 15,
+                effort: 'Medium',
+                surgeryIds: [surgery.id],
+                fromOR: overutilizedOR,
+                toOR: orId,
+                estimatedSavings: 'Improved resource balance',
+                risks: ['Requires staff coordination'],
+              });
+            }
+          }
+        }
+      });
+      
+      return suggestions;
+    },
+
+    // Generate conflict resolution suggestions
+    generateConflictResolutionSuggestions(surgeries) {
+      const suggestions = [];
+      
+      // Find surgeries with conflicts
+      const conflictedSurgeries = surgeries.filter(s => s.conflicts && s.conflicts.length > 0);
+      
+      conflictedSurgeries.forEach(surgery => {
+        surgery.conflicts.forEach(conflict => {
+          if (conflict.includes('SDST Violation')) {
+            suggestions.push({
+              id: `conflict-${Date.now()}-${surgery.id}`,
+              type: 'reschedule',
+              category: 'Conflict Resolution',
+              priority: 'High',
+              title: `Resolve SDST conflict for ${surgery.type} surgery`,
+              description: `Reschedule ${surgery.patientName}'s surgery to allow adequate setup time`,
+              impact: 25,
+              effort: 'High',
+              surgeryIds: [surgery.id],
+              conflictType: 'SDST Violation',
+              estimatedSavings: 'Eliminates scheduling conflict',
+              risks: ['Patient notification required', 'Schedule disruption'],
+            });
+          }
+        });
+      });
+      
+      return suggestions;
+    },
+
+    // Generate schedule balancing suggestions
+    generateScheduleBalancingSuggestions(surgeries) {
+      const suggestions = [];
+      
+      // Analyze daily workload distribution
+      const dailyWorkload = {};
+      surgeries.forEach(surgery => {
+        const date = surgery.startTime.split('T')[0];
+        if (!dailyWorkload[date]) dailyWorkload[date] = { count: 0, duration: 0 };
+        dailyWorkload[date].count++;
+        dailyWorkload[date].duration += surgery.duration;
+      });
+      
+      // Find imbalanced days
+      const avgWorkload = Object.values(dailyWorkload).reduce((sum, day) => sum + day.duration, 0) / Object.keys(dailyWorkload).length;
+      
+      Object.entries(dailyWorkload).forEach(([date, workload]) => {
+        if (workload.duration > avgWorkload * 1.3) {
+          suggestions.push({
+            id: `balance-${Date.now()}-${date}`,
+            type: 'redistribute',
+            category: 'Schedule Balancing',
+            priority: 'Medium',
+            title: `Redistribute workload for ${date}`,
+            description: `Move non-urgent surgeries from overloaded day to balance schedule`,
+            impact: 20,
+            effort: 'Medium',
+            targetDate: date,
+            estimatedSavings: 'Better workload distribution',
+            risks: ['Requires patient coordination'],
+          });
+        }
+      });
+      
+      return suggestions;
+    },
+
+    // Helper method to get SDST between surgery types
+    getSDSTBetweenTypes(fromType, toType) {
+      // This would normally come from the SDST matrix
+      const sdstMatrix = {
+        'APPEN->KNEE': 15,
+        'KNEE->HIPRE': 15,
+        'CABG->APPEN': 45,
+        'CABG->CATAR': 45,
+        'HIPRE->CABG': 45,
+        'CATAR->HIPRE': 30,
+        // Add more transitions as needed
+      };
+      
+      const key = `${fromType}->${toType}`;
+      return sdstMatrix[key] || 25; // Default SDST
+    },
+
+    // Analyze resource usage
+    analyzeResourceUsage(surgeries) {
+      const orUtilization = {};
+      const staffUtilization = {};
+      const equipmentUtilization = {};
+      
+      // Calculate OR utilization (simplified)
+      surgeries.forEach(surgery => {
+        if (!orUtilization[surgery.orId]) orUtilization[surgery.orId] = 0;
+        orUtilization[surgery.orId] += surgery.duration / (8 * 60); // Assuming 8-hour days
+      });
+      
+      return {
+        orUtilization,
+        staffUtilization,
+        equipmentUtilization,
+      };
+    },
+
+    // Calculate optimization metrics
+    calculateOptimizationMetrics(originalSurgeries, suggestions) {
+      const sdstReduction = suggestions
+        .filter(s => s.category === 'SDST Optimization')
+        .reduce((sum, s) => sum + s.impact, 0);
+      
+      const conflictReduction = suggestions
+        .filter(s => s.category === 'Conflict Resolution')
+        .length;
+      
+      const utilizationImprovement = suggestions
+        .filter(s => s.category === 'Resource Optimization')
+        .length * 5; // Simplified calculation
+      
+      return {
+        sdstReduction,
+        utilizationImprovement,
+        conflictReduction,
+        timesSaved: sdstReduction + utilizationImprovement,
+        overallImpact: sdstReduction > 60 ? 'High' : sdstReduction > 30 ? 'Medium' : 'Low',
+      };
+    },
+
+    // Apply selected suggestions
+    async applySuggestions(suggestionIds) {
+      const scheduleStore = useScheduleStore();
+      const applicableSuggestions = this.currentSuggestions.filter(s => 
+        suggestionIds.includes(s.id)
+      );
+      
+      for (const suggestion of applicableSuggestions) {
+        try {
+          await this.applySingleSuggestion(suggestion);
+          this.optimizationMetrics.suggestionsApplied++;
+        } catch (error) {
+          console.error('Failed to apply suggestion:', suggestion.id, error);
+        }
+      }
+      
+      // Update optimization history
+      const historyEntry = this.optimizationHistory[0];
+      if (historyEntry) {
+        historyEntry.applied = true;
+        historyEntry.appliedCount = suggestionIds.length;
+      }
+      
+      // Recalculate metrics
+      await this.updateOptimizationMetrics();
+    },
+
+    // Apply a single suggestion
+    async applySingleSuggestion(suggestion) {
+      const scheduleStore = useScheduleStore();
+      
+      switch (suggestion.type) {
+        case 'reorder':
+          await this.applySurgeryReorder(suggestion);
+          break;
+        case 'relocate':
+          await this.applySurgeryRelocation(suggestion);
+          break;
+        case 'reschedule':
+          await this.applySurgeryReschedule(suggestion);
+          break;
+        default:
+          console.warn('Unknown suggestion type:', suggestion.type);
+      }
+    },
+
+    // Apply surgery reorder
+    async applySurgeryReorder(suggestion) {
+      const scheduleStore = useScheduleStore();
+      const [firstId, secondId] = suggestion.suggestedOrder;
+      
+      // Get the surgeries
+      const firstSurgery = scheduleStore.scheduledSurgeries.find(s => s.id === firstId);
+      const secondSurgery = scheduleStore.scheduledSurgeries.find(s => s.id === secondId);
+      
+      if (firstSurgery && secondSurgery) {
+        // Swap their start times
+        const tempStartTime = firstSurgery.startTime;
+        firstSurgery.startTime = secondSurgery.startTime;
+        secondSurgery.startTime = tempStartTime;
+        
+        // Update end times
+        firstSurgery.endTime = new Date(new Date(firstSurgery.startTime).getTime() + firstSurgery.duration * 60000).toISOString();
+        secondSurgery.endTime = new Date(new Date(secondSurgery.startTime).getTime() + secondSurgery.duration * 60000).toISOString();
+        
+        console.log('Applied surgery reorder:', suggestion.title);
+      }
+    },
+
+    // Apply surgery relocation
+    async applySurgeryRelocation(suggestion) {
+      const scheduleStore = useScheduleStore();
+      const surgery = scheduleStore.scheduledSurgeries.find(s => s.id === suggestion.surgeryIds[0]);
+      
+      if (surgery) {
+        surgery.orId = suggestion.toOR;
+        surgery.orName = `Operating Room ${suggestion.toOR.replace('OR', '')}`;
+        console.log('Applied surgery relocation:', suggestion.title);
+      }
+    },
+
+    // Apply surgery reschedule
+    async applySurgeryReschedule(suggestion) {
+      // This would involve more complex logic to find a new time slot
+      console.log('Surgery reschedule would be applied:', suggestion.title);
+    },
+
+    // Update optimization metrics
+    async updateOptimizationMetrics() {
+      // Recalculate average improvements
+      const history = this.optimizationHistory.filter(h => h.applied);
+      if (history.length > 0) {
+        this.optimizationMetrics.averageSDSTImprovement = 
+          history.reduce((sum, h) => sum + h.potentialSavings, 0) / history.length;
+      }
+    },
+
+    // Clear optimization results
+    clearOptimizationResults() {
+      this.optimizationResults = null;
+      this.selectedSuggestions = [];
+    },
+
+    // Update optimization settings
+    updateOptimizationSettings(newSettings) {
+      this.optimizationSettings = { ...this.optimizationSettings, ...newSettings };
+    },
+
+    // Toggle suggestion selection
+    toggleSuggestionSelection(suggestionId) {
+      const index = this.selectedSuggestions.indexOf(suggestionId);
+      if (index > -1) {
+        this.selectedSuggestions.splice(index, 1);
+      } else {
+        this.selectedSuggestions.push(suggestionId);
+      }
+    },
+
+    // Select all suggestions
+    selectAllSuggestions() {
+      this.selectedSuggestions = this.currentSuggestions.map(s => s.id);
+    },
+
+    // Clear all selections
+    clearAllSelections() {
+      this.selectedSuggestions = [];
+    },
+  },
+});
diff --git a/src/stores/scheduleStore.js b/src/stores/scheduleStore.js
index f80fb74..960ac80 100644
--- a/src/stores/scheduleStore.js
+++ b/src/stores/scheduleStore.js
@@ -756,6 +756,66 @@ export const useScheduleStore = defineStore('schedule', {
          console.error("Deleting surgery type failed:", err);
          this.isLoading = false;
        }
+     },
+
+     // Real-time SDST calculation for drag and drop operations
+     calculateSDSTForPosition(surgery, targetORId, proposedStartTime) {
+       const conflicts = [];
+       let sdsTime = 0;
+
+       // Get surgeries in the target OR, sorted by time, excluding the surgery being moved
+       const surgeriesInOR = this.scheduledSurgeries
+         .filter(s => s.orId === targetORId && s.id !== surgery.id)
+         .sort((a, b) => new Date(a.startTime) - new Date(b.startTime));
+
+       // Find preceding surgery
+       const precedingSurgery = surgeriesInOR
+         .filter(s => new Date(s.endTime) <= proposedStartTime)
+         .pop(); // Get the last one before proposed time
+
+       // Calculate SDST
+       if (precedingSurgery) {
+         const precedingType = precedingSurgery.type;
+         sdsTime = this.sdsRules[precedingType]?.[surgery.type] || 0;
+       } else {
+         // First surgery of the day
+         sdsTime = this.initialSetupTimes[surgery.type] || 0;
+       }
+
+       // Check for SDST violation
+       if (precedingSurgery) {
+         const gapBefore = (proposedStartTime.getTime() - new Date(precedingSurgery.endTime).getTime()) / (1000 * 60);
+         if (gapBefore < sdsTime) {
+           conflicts.push(`SDST Violation: Requires ${sdsTime} min setup, only ${Math.max(0, Math.floor(gapBefore))} min available`);
+         }
+       }
+
+       // Check for overlaps with existing surgeries
+       const proposedEndTime = new Date(proposedStartTime.getTime() + (surgery.estimatedDuration + sdsTime) * 60 * 1000);
+
+       surgeriesInOR.forEach(existingSurgery => {
+         const existingStart = new Date(existingSurgery.startTime);
+         const existingEnd = new Date(existingSurgery.endTime);
+
+         // Check for time overlap
+         if (proposedStartTime < existingEnd && proposedEndTime > existingStart) {
+           conflicts.push(`Time conflict with ${existingSurgery.patientName} (${existingStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - ${existingEnd.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })})`);
+         }
+       });
+
+       // Check for surgeon conflicts
+       const surgeonConflicts = this.scheduledSurgeries.filter(s =>
+         s.id !== surgery.id &&
+         s.surgeonId === surgery.surgeonId &&
+         proposedStartTime < new Date(s.endTime) &&
+         proposedEndTime > new Date(s.startTime)
+       );
+
+       surgeonConflicts.forEach(conflict => {
+         conflicts.push(`Surgeon ${surgery.requiredSurgeons?.[0] || 'conflict'} unavailable (scheduled in ${conflict.orName})`);
+       });
+
+       return { sdsTime, conflicts };
      }
   }
 });
diff --git a/src/style.css b/src/style.css
index efcd0bf..f751db6 100644
--- a/src/style.css
+++ b/src/style.css
@@ -76,12 +76,15 @@
   --spacing-md: 16px;
   --spacing-lg: 24px;
   --spacing-xl: 32px;
+  --spacing-xxl: 48px;
 
   /* Typography */
+  --font-size-xs: 0.75rem; /* ~12px */
   --font-size-sm: 0.875rem; /* ~14px */
   --font-size-base: 1rem; /* ~16px */
   --font-size-lg: 1.125rem; /* ~18px */
   --font-size-xl: 1.25rem; /* ~20px */
+  --font-size-xxl: 1.5rem; /* ~24px */
   --font-weight-normal: 400;
   --font-weight-medium: 500;
   --font-weight-bold: 700;
@@ -89,14 +92,36 @@
   /* Border Radius */
   --border-radius-sm: 4px;
   --border-radius-md: 8px;
+  --border-radius-lg: 12px;
 
-  /* Z-index (Example) */
+  /* Z-index */
   --z-index-tooltip: 100;
   --z-index-modal: 1000;
-
-  /* Layout (Example, replace with actual header/footer height if they exist in a layout component) */
-  --header-height: 0px; /* Assuming no fixed header within this scope for now */
-  --footer-height: 0px; /* Assuming no fixed footer within this scope for now */
+  --z-index-mobile-nav: 1100;
+
+  /* Layout */
+  --header-height: 60px;
+  --footer-height: 0px;
+  --sidebar-width: 240px;
+  --sidebar-width-collapsed: 60px;
+
+  /* Mobile Breakpoints */
+  --breakpoint-xs: 480px;
+  --breakpoint-sm: 768px;
+  --breakpoint-md: 1024px;
+  --breakpoint-lg: 1200px;
+  --breakpoint-xl: 1440px;
+
+  /* Mobile-specific spacing */
+  --mobile-spacing-xs: 2px;
+  --mobile-spacing-sm: 4px;
+  --mobile-spacing-md: 8px;
+  --mobile-spacing-lg: 12px;
+  --mobile-spacing-xl: 16px;
+
+  /* Touch targets */
+  --touch-target-min: 44px;
+  --touch-target-comfortable: 48px;
 }
 
 /* Global box-sizing for easier layout calculations */
@@ -238,3 +263,154 @@ select:focus {
   clip: rect(0, 0, 0, 0);
   border: 0;
 }
+
+/* ===== MOBILE RESPONSIVE UTILITIES ===== */
+
+/* Mobile-first responsive design utilities */
+.mobile-only {
+  display: block;
+}
+
+.desktop-only {
+  display: none;
+}
+
+.tablet-only {
+  display: none;
+}
+
+/* Touch-friendly button sizing */
+.btn-touch {
+  min-height: var(--touch-target-min);
+  min-width: var(--touch-target-min);
+  padding: var(--spacing-sm) var(--spacing-md);
+}
+
+/* Mobile-friendly form controls */
+.form-control-mobile {
+  min-height: var(--touch-target-comfortable);
+  font-size: var(--font-size-base); /* Prevent zoom on iOS */
+}
+
+/* Mobile spacing utilities */
+.mobile-p-xs { padding: var(--mobile-spacing-xs); }
+.mobile-p-sm { padding: var(--mobile-spacing-sm); }
+.mobile-p-md { padding: var(--mobile-spacing-md); }
+.mobile-p-lg { padding: var(--mobile-spacing-lg); }
+.mobile-p-xl { padding: var(--mobile-spacing-xl); }
+
+.mobile-m-xs { margin: var(--mobile-spacing-xs); }
+.mobile-m-sm { margin: var(--mobile-spacing-sm); }
+.mobile-m-md { margin: var(--mobile-spacing-md); }
+.mobile-m-lg { margin: var(--mobile-spacing-lg); }
+.mobile-m-xl { margin: var(--mobile-spacing-xl); }
+
+/* Mobile layout utilities */
+.mobile-stack {
+  flex-direction: column;
+}
+
+.mobile-full-width {
+  width: 100%;
+}
+
+.mobile-text-center {
+  text-align: center;
+}
+
+.mobile-hidden {
+  display: none;
+}
+
+/* Responsive grid system */
+.responsive-grid {
+  display: grid;
+  gap: var(--spacing-md);
+  grid-template-columns: 1fr;
+}
+
+.responsive-flex {
+  display: flex;
+  flex-wrap: wrap;
+  gap: var(--spacing-md);
+}
+
+.responsive-flex > * {
+  flex: 1;
+  min-width: 0;
+}
+
+/* ===== MEDIA QUERIES ===== */
+
+/* Small devices (landscape phones, 480px and up) */
+@media (min-width: 480px) {
+  .mobile-only {
+    display: none;
+  }
+
+  .tablet-only {
+    display: block;
+  }
+
+  .responsive-grid {
+    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
+  }
+}
+
+/* Medium devices (tablets, 768px and up) */
+@media (min-width: 768px) {
+  .tablet-only {
+    display: none;
+  }
+
+  .desktop-only {
+    display: block;
+  }
+
+  .mobile-hidden {
+    display: block;
+  }
+
+  .mobile-stack {
+    flex-direction: row;
+  }
+
+  .mobile-full-width {
+    width: auto;
+  }
+
+  .mobile-text-center {
+    text-align: left;
+  }
+
+  /* Reset mobile spacing on larger screens */
+  .mobile-p-xs { padding: var(--spacing-xs); }
+  .mobile-p-sm { padding: var(--spacing-sm); }
+  .mobile-p-md { padding: var(--spacing-md); }
+  .mobile-p-lg { padding: var(--spacing-lg); }
+  .mobile-p-xl { padding: var(--spacing-xl); }
+
+  .mobile-m-xs { margin: var(--spacing-xs); }
+  .mobile-m-sm { margin: var(--spacing-sm); }
+  .mobile-m-md { margin: var(--spacing-md); }
+  .mobile-m-lg { margin: var(--spacing-lg); }
+  .mobile-m-xl { margin: var(--spacing-xl); }
+
+  .responsive-grid {
+    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
+  }
+}
+
+/* Large devices (desktops, 1024px and up) */
+@media (min-width: 1024px) {
+  .responsive-grid {
+    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
+  }
+}
+
+/* Extra large devices (large desktops, 1200px and up) */
+@media (min-width: 1200px) {
+  .responsive-grid {
+    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
+  }
+}
