{"name": "surgery-scheduler-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test": "vue-cli-service test:unit"}, "dependencies": {"axios": "^1.3.4", "core-js": "^3.8.3", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0", "primevue": "^3.26.1", "primeicons": "^6.0.1", "primeflex": "^3.3.0", "chart.js": "^4.2.1", "vue-chartjs": "^5.2.0", "moment": "^2.29.4", "jwt-decode": "^3.1.2", "socket.io-client": "^4.6.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^2.0.0", "@vue/vue3-jest": "^27.0.0", "babel-jest": "^27.0.6", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "jest": "^27.0.5"}}