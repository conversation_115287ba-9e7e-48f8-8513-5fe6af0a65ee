# Database Configuration
# Option 1: Direct DATABASE_URL
# DATABASE_URL=mysql+pymysql://username:password@localhost:3306/surgery_scheduler
# DATABASE_URL=sqlite:///./surgery_scheduler.db

# Option 2: Individual MySQL components
DB_USER=username
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=surgery_scheduler

# SQLite fallback (used if MySQL parameters are not set)
SQLITE_URL=sqlite:///./surgery_scheduler.db

# SQL Echo for debugging (set to True to see SQL queries)
SQL_ECHO=False

# Email Configuration for Notifications
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# Google Calendar API
GOOGLE_CALENDAR_ID=<EMAIL>
CALENDAR_TIMEZONE=America/New_York

# Audit Logging
AUDIT_LOG_TO_FILE=True
AUDIT_LOG_FILE=logs/audit.log

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/surgery_scheduler.log
LOG_FORMAT=text  # text or json

# Application Settings
SECRET_KEY=your_secret_key_here
DEBUG=False
TESTING=False

# FastAPI Settings
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=True

# JWT Authentication
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
CORS_ORIGINS=http://localhost:8080,http://localhost:3000
